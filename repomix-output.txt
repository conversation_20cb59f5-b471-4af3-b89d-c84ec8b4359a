This file is a merged representation of the entire codebase, combining all repository files into a single document.
Generated by Repomix on: 2024-12-16T18:02:40.513Z

================================================================
File Summary
================================================================

Purpose:
--------
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

File Format:
------------
The content is organized as follows:
1. This summary section
2. Repository information
3. Repository structure
4. Multiple file entries, each consisting of:
  a. A separator line (================)
  b. The file path (File: path/to/file)
  c. Another separator line
  d. The full contents of the file
  e. A blank line

Usage Guidelines:
-----------------
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

Notes:
------
- Some files may have been excluded based on .gitignore rules and Repomix's
  configuration.
- Binary files are not included in this packed representation. Please refer to
  the Repository Structure section for a complete list of file paths, including
  binary files.

Additional Info:
----------------

For more information about Repomix, visit: https://github.com/yamadashy/repomix

================================================================
Repository Structure
================================================================
app/
  src/
    androidTest/
      java/
        dev/
          paypass/
            ExampleInstrumentedTest.kt
    main/
      java/
        dev/
          paypass/
            data/
              auth/
                AuthInfoStorage.kt
                AuthRepositoryImpl.kt
              config/
                work/
                  LoadConfigSchedulerImpl.kt
                  LoadConfigWorker.kt
                RemoteConfigMapper.kt
                RemoteConfigRepositoryImpl.kt
                RemoteConfigStorage.kt
              event/
                db/
                  EventDao.kt
                  EventEntity.kt
                work/
                  SendEventSchedulerImpl.kt
                  SendEventWorker.kt
                EventMapper.kt
                EventRepositoryImpl.kt
              keeplife/
                KeepLifeManagerImpl.kt
              network/
                config/
                  ConfigRequestBody.kt
                  ConfigResponseBody.kt
                event/
                  EventRequestBody.kt
                BackendApi.kt
                EncryptionInterceptor.kt
                EncryptToField.kt
              work/
                WorkDataExtensions.kt
              AppDatabase.kt
              WorkInfoManagerImpl.kt
            di/
              module/
                AppModule.kt
                ComponentModule.kt
                DataModule.kt
                DomainModule.kt
                NetworkModule.kt
                SystemModule.kt
              KoinComponentFactory.kt
            domain/
              auth/
                use_case/
                  AuthInfoChangesUseCase.kt
                  AuthInfoChangesUseCaseImpl.kt
                  AuthSavePhoneNumberUseCase.kt
                  AuthSavePhoneNumberUseCaseImpl.kt
                  AuthSaveTokenUseCase.kt
                  AuthSaveTokenUseCaseImpl.kt
                  AuthScanQrResult.kt
                  AuthScanQrUseCase.kt
                  AuthScanQrUseCaseImpl.kt
                AuthInfo.kt
                AuthRepository.kt
              condition/
                AppCondition.kt
                GetAllAppConditionsUseCase.kt
                GetAllAppConditionsUseCaseImpl.kt
              config/
                use_case/
                  LoadConfigByAuthChangesUseCase.kt
                  LoadConfigByAuthChangesUseCaseImpl.kt
                  RemoteConfigChangesUseCase.kt
                  RemoteConfigChangesUseCaseImpl.kt
                LoadConfigScheduler.kt
                RemoteConfig.kt
                RemoteConfigRepository.kt
              event/
                use_case/
                  SaveSmsEventsUseCase.kt
                  SaveSmsEventsUseCaseImpl.kt
                  SaveSystemNotificationEventsUseCase.kt
                  SaveSystemNotificationEventsUseCaseImpl.kt
                  SendNotProcessedEventsUseCase.kt
                  SendNotProcessedEventsUseCaseImpl.kt
                EventRepository.kt
                SendEventScheduler.kt
              keeplife/
                KeepLifeManager.kt
                StartKeepLifeUseCase.kt
                StartKeepLifeUseCaseImpl.kt
              startup/
                StartupUseCase.kt
                StartupUseCaseImpl.kt
              work/
                WorkInfo.kt
                WorkInfoManager.kt
              Event.kt
            system/
              activity/
                CurrentActivityProvider.kt
                CurrentActivityProviderImpl.kt
              notification/
                AppNotificationListenerService.kt
                ReceivedNotification.kt
                SystemNotificationsManager.kt
                SystemNotificationsManagerImpl.kt
              permission/
                Permission.kt
                PermissionsManager.kt
                PermissionsManagerImpl.kt
                PermissionStatus.kt
              sms/
                IncomingSms.kt
                IncomingSmsBroadcastReceiver.kt
                IncomingSmsReceiver.kt
                SmsManager.kt
                SmsManagerImpl.kt
              storage/
                impl/
                  DataStoreSerializer.kt
                  DataStoreStorage.kt
                  DataStoreStorageFactory.kt
                  InMemoryStorage.kt
                  InMemoryStorageFactory.kt
                  JsonDataStoreSerializer.kt
                  StorageFactoryProviderImpl.kt
                Storage.kt
                StorageExtensions.kt
                StorageFactory.kt
                StorageFactoryProvider.kt
                StorageType.kt
              thirdparty/
                QrScanLauncher.kt
                QrScanLauncherImpl.kt
                QrScanResult.kt
              util/
                DefaultActivityLifecycleCallbacks.kt
            ui/
              component/
                app_conditions/
                  widget/
                    NotificationListenersDialog.kt
                    PermissionDialog.kt
                  AppConditionsScreenComponent.kt
                  AppConditionsScreenComponentImpl.kt
                  AppConditionsScreenState.kt
                  AppConditionsScreenUI.kt
                main/
                  widget/
                    MainAuthBlock.kt
                    MainCameraPermissionDialog.kt
                    MainDebugInfoBlock.kt
                  MainScreenComponent.kt
                  MainScreenComponentImpl.kt
                  MainScreenNode.kt
                  MainScreenState.kt
                  MainScreenUI.kt
                RootComponent.kt
                RootComponentImpl.kt
                RootUI.kt
              core/
                text/
                  MaskOffsetMapping.kt
                  TextMaskedVisualTransformation.kt
              theme/
                Color.kt
                Theme.kt
                Type.kt
              util/
                DecomposeExtensions.kt
              ComponentFactory.kt
              MainActivity.kt
            BootReceiver.kt
            KeepLifeService.kt
            StartupInitializer.kt
      AndroidManifest.xml
    test/
      java/
        dev/
          paypass/
            ExampleUnitTest.kt
  build.gradle.kts
build.gradle.kts
settings.gradle.kts

================================================================
Repository Files
================================================================

================
File: app/src/androidTest/java/dev/paypass/ExampleInstrumentedTest.kt
================
package dev.paypass

import androidx.test.platform.app.InstrumentationRegistry
import androidx.test.ext.junit.runners.AndroidJUnit4

import org.junit.Test
import org.junit.runner.RunWith

import org.junit.Assert.*

/**
 * Instrumented test, which will execute on an Android device.
 *
 * See [testing documentation](http://d.android.com/tools/testing).
 */
@RunWith(AndroidJUnit4::class)
class ExampleInstrumentedTest {
    @Test
    fun useAppContext() {
        // Context of the app under test.
        val appContext = InstrumentationRegistry.getInstrumentation().targetContext
        assertEquals("dev.paypass", appContext.packageName)
    }
}

================
File: app/src/main/java/dev/paypass/data/auth/AuthInfoStorage.kt
================
package dev.paypass.data.auth

import dev.paypass.domain.auth.AuthInfo
import dev.paypass.system.storage.Storage
import dev.paypass.system.storage.StorageFactoryProvider
import dev.paypass.system.storage.StorageType

internal class AuthInfoStorage(
    private val storageFactoryProvider: StorageFactoryProvider
) : Storage<AuthInfo> by storageFactoryProvider.getStorageFactory(
    storageType = StorageType.PERSISTENT
).createStorage(
    key = "auth_info",
    clazz = AuthInfo::class.java
)

================
File: app/src/main/java/dev/paypass/data/auth/AuthRepositoryImpl.kt
================
package dev.paypass.data.auth

import dev.paypass.domain.auth.AuthInfo
import dev.paypass.domain.auth.AuthRepository
import dev.paypass.system.storage.update
import kotlinx.coroutines.flow.Flow

internal class AuthRepositoryImpl(
    private val authInfoStorage: AuthInfoStorage
) : AuthRepository {

    override fun authInfoChanges(): Flow<AuthInfo> {
        return authInfoStorage.changesFlow()
    }

    override suspend fun savePhoneNumber(phoneNumber: String) {
        authInfoStorage.update { authInfo ->
            authInfo?.copy(id = phoneNumber) ?: AuthInfo(id = phoneNumber, token = "")
        }
    }

    override suspend fun saveToken(token: String) {
        authInfoStorage.update { authInfo ->
            authInfo?.copy(token = token) ?: AuthInfo(id = "", token = token)
        }
    }
}

================
File: app/src/main/java/dev/paypass/data/config/work/LoadConfigSchedulerImpl.kt
================
package dev.paypass.data.config.work

import androidx.work.Constraints
import androidx.work.NetworkType
import androidx.work.PeriodicWorkRequest
import androidx.work.WorkManager
import dev.paypass.domain.auth.AuthInfo
import dev.paypass.domain.config.LoadConfigScheduler
import java.util.concurrent.TimeUnit

class LoadConfigSchedulerImpl(
    private val workManager: WorkManager,
) : LoadConfigScheduler {

    override fun schedule(authInfo: AuthInfo) {
        val inputData = LoadConfigWorker.Input(
            authInfo = authInfo,
            appVersion = "alpha"
        ).toWorkerData()

        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .build()

        val loadConfigWorkRequest = PeriodicWorkRequest.Builder(
            LoadConfigWorker::class.java,
            INTERVAL,
            INTERVAL_UNIT
        )
            .setInputData(inputData)
            .setConstraints(constraints)
            .build()

        workManager.enqueue(loadConfigWorkRequest)
    }

    companion object {
        private const val INTERVAL = 1L
        private val INTERVAL_UNIT = TimeUnit.DAYS
    }
}

================
File: app/src/main/java/dev/paypass/data/config/work/LoadConfigWorker.kt
================
package dev.paypass.data.config.work

import android.content.Context
import androidx.work.CoroutineWorker
import androidx.work.Data
import androidx.work.WorkerParameters
import dev.paypass.data.work.getSerialized
import dev.paypass.data.work.putSerialized
import dev.paypass.domain.auth.AuthInfo
import dev.paypass.domain.config.RemoteConfigRepository
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

internal class LoadConfigWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params),
    KoinComponent {

    private val remoteConfigRepository: RemoteConfigRepository by inject()

    override suspend fun doWork(): Result {
        val input = Input.fromWorkerData(inputData)

        val loadResult = remoteConfigRepository.loadRemoteConfig(
            phoneNumber = input.authInfo.id,
            token = input.authInfo.token,
            appVersion = input.appVersion
        )

        return if (loadResult.isSuccess) {
            Result.success()
        } else {
            Result.retry()
        }
    }

    data class Input(
        val authInfo: AuthInfo,
        val appVersion: String
    ) {

        fun toWorkerData(): Data {
            return Data.Builder()
                .putSerialized(AUTH_INFO_KEY, authInfo, AuthInfo.serializer())
                .putString(APP_VERSION_KEY, appVersion)
                .build()
        }

        companion object {
            private const val AUTH_INFO_KEY = "authInfo"
            private const val APP_VERSION_KEY = "appVersion"

            fun fromWorkerData(data: Data): Input {
                val authInfo = data.getSerialized(AUTH_INFO_KEY, AuthInfo.serializer()) ?: error("Auth info is missing")
                val appVersion = data.getString(APP_VERSION_KEY) ?: error("App version is missing")

                return Input(authInfo, appVersion)
            }
        }
    }
}

================
File: app/src/main/java/dev/paypass/data/config/RemoteConfigMapper.kt
================
package dev.paypass.data.config

import dev.paypass.domain.config.RemoteConfig
import dev.paypass.data.network.config.ConfigResponseBody

internal object RemoteConfigMapper {

    fun networkToDomain(
        body: ConfigResponseBody
    ): RemoteConfig {
        return RemoteConfig(
            urlForPush = body.urlForPush,
            urlForSms = body.urlForSms,
            urlForHeartBeat = "not implemented"
        )
    }
}

================
File: app/src/main/java/dev/paypass/data/config/RemoteConfigRepositoryImpl.kt
================
package dev.paypass.data.config

import dev.paypass.domain.config.RemoteConfig
import dev.paypass.domain.config.RemoteConfigRepository
import dev.paypass.data.network.BackendApi
import dev.paypass.data.network.config.ConfigRequestBody
import kotlinx.coroutines.flow.Flow
import timber.log.Timber

internal class RemoteConfigRepositoryImpl(
    private val backendApi: BackendApi,
    private val storage: RemoteConfigStorage
) : RemoteConfigRepository {

    override fun remoteConfigChanges(): Flow<RemoteConfig> {
        return storage.changesFlow()
    }

    override suspend fun loadRemoteConfig(
        phoneNumber: String,
        token: String,
        appVersion: String
    ): Result<RemoteConfig> {
        val requestBody = ConfigRequestBody(
            phoneNumber = phoneNumber,
            token = token,
            pppVersion = appVersion
        )

        try {
            val response = backendApi.getConfig(requestBody)

            if (response.isSuccessful) {
                val body = response.body() ?: error("Response body is null")
                val remoteConfig = RemoteConfigMapper.networkToDomain(body)

                storage.update(remoteConfig)

                return Result.success(remoteConfig)
            } else {
                return Result.failure(Exception("Failed to load remote config: ${response.errorBody()?.string()}"))
            }
        } catch (e: Exception) {
            Timber.e(e, "Internal error")
            return Result.failure(Exception("Internal error: ${e.message}"))
        }
    }
}

================
File: app/src/main/java/dev/paypass/data/config/RemoteConfigStorage.kt
================
package dev.paypass.data.config

import dev.paypass.domain.config.RemoteConfig
import dev.paypass.system.storage.Storage
import dev.paypass.system.storage.StorageFactoryProvider
import dev.paypass.system.storage.StorageType

internal class RemoteConfigStorage(
    private val storageFactoryProvider: StorageFactoryProvider
) : Storage<RemoteConfig> by storageFactoryProvider.getStorageFactory(
    storageType = StorageType.PERSISTENT
).createStorage(
    key = "remote_config",
    clazz = RemoteConfig::class.java
)

================
File: app/src/main/java/dev/paypass/data/event/db/EventDao.kt
================
package dev.paypass.data.event.db

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import kotlinx.coroutines.flow.Flow

@Dao
interface EventDao {

    @Query("SELECT * FROM events WHERE isProcessed = 0 ORDER BY timestamp")
    fun getNotProcessedEvents(): Flow<List<EventEntity>>

    @Query("SELECT * FROM events ORDER BY timestamp")
    suspend fun getEvents(): List<EventEntity>

    @Insert
    suspend fun insertEvent(event: EventEntity)

    @Query("UPDATE events SET isProcessed = :isProcessed, errorReasonType = :errorReasonType, errorReasonServerCode = :errorReasonServerCode, errorReasonMessage = :errorReasonMessage WHERE id = :id")
    suspend fun updateEvent(
        id: Long,
        isProcessed: Boolean,
        errorReasonType: EventEntity.ErrorReasonType? = null,
        errorReasonServerCode: Int? = null,
        errorReasonMessage: String? = null
    )
}

================
File: app/src/main/java/dev/paypass/data/event/db/EventEntity.kt
================
package dev.paypass.data.event.db

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "events")
data class EventEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val type: Type,
    val timestamp: Long,
    val author: String,
    val message: String,
    val packageName: String? = null,
    val isProcessed: Boolean = false,
    val errorReasonType: ErrorReasonType? = null,
    val errorReasonServerCode: Int? = null,
    val errorReasonMessage: String? = null
) {

    enum class Type {
        SMS,
        NOTIFICATION
    }

    enum class ErrorReasonType {
        NO_INTERNET,
        SERVER_ERROR,
        UNKNOWN
    }
}

================
File: app/src/main/java/dev/paypass/data/event/work/SendEventSchedulerImpl.kt
================
package dev.paypass.data.event.work

import androidx.work.BackoffPolicy
import androidx.work.Constraints
import androidx.work.ExistingWorkPolicy
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import dev.paypass.domain.Event
import dev.paypass.domain.event.SendEventScheduler
import timber.log.Timber
import kotlin.time.Duration.Companion.minutes
import kotlin.time.toJavaDuration

internal class SendEventSchedulerImpl(
    private val workManager: WorkManager
) : SendEventScheduler {

    override fun schedule(
        userId: String,
        userToken: String,
        url: String,
        event: Event,
    ) {
        val inputData = SendEventWorker.Input(
            userId = userId,
            userToken = userToken,
            url = url,
            event = event
        ).toWorkerData()

        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .build()

        val workRequest = OneTimeWorkRequestBuilder<SendEventWorker>()
            .setConstraints(constraints)
            .setBackoffCriteria(BackoffPolicy.LINEAR, DEFAULT_BACKOFF_DELAY.toJavaDuration())
            .setInputData(inputData)
            .addTag("send_event")
            .build()

        val configHash = (url + userId + userToken).hashCode()
        val workName = "send_event_${event.timestamp}_${event.message}_configHash_$configHash"

        val isWorkScheduled = workManager.getWorkInfosForUniqueWork(workName).get().any { it.state.isFinished.not() }

        if (isWorkScheduled) {
            Timber.d("Work $workName is already scheduled")
        } else {
            Timber.d("Scheduling work $workName")
        }

        workManager.enqueueUniqueWork(
            uniqueWorkName = workName,
            existingWorkPolicy = ExistingWorkPolicy.KEEP,
            request = workRequest
        )
    }

    companion object {
        private val DEFAULT_BACKOFF_DELAY = 1.minutes
    }
}

================
File: app/src/main/java/dev/paypass/data/event/work/SendEventWorker.kt
================
package dev.paypass.data.event.work

import android.content.Context
import androidx.work.CoroutineWorker
import androidx.work.Data
import androidx.work.WorkerParameters
import dev.paypass.data.work.getSerialized
import dev.paypass.data.work.putSerialized
import dev.paypass.domain.Event
import dev.paypass.domain.event.EventRepository
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import timber.log.Timber

internal class SendEventWorker(
    context: Context,
    workerParams: WorkerParameters
) : CoroutineWorker(context, workerParams),
    KoinComponent {

    private val eventRepository: EventRepository by inject()

    override suspend fun doWork(): Result {
        val input = runCatching { Input.fromWorkerData(inputData) }
            .getOrElse { error ->
                Timber.e(error, "Failed to parse input data. input data: $inputData")
                return Result.failure()
            }

        val result = eventRepository.sendEvent(
            userId = input.userId,
            userToken = input.userToken,
            url = input.url,
            event = input.event
        )

        return if (result.isSuccess) {
            Timber.d("Event sent successfully. Event: $input")
            Result.success()
        } else if (runAttemptCount < DEFAULT_MAX_ATTEMPTS) {
            Timber.d("Failed to send event. Attempt $runAttemptCount. Retrying...")
            Result.retry()
        } else {
            Timber.e("Failed to send event after $DEFAULT_MAX_ATTEMPTS attempts. Event: $input", result.exceptionOrNull())
            Result.failure()
        }
    }

    data class Input(
        val userId: String,
        val userToken: String,
        val url: String,
        val event: Event
    ) {

        fun toWorkerData(): Data {
            return Data.Builder()
                .putString(INPUT_DATA_USER_ID_KEY, userId)
                .putString(INPUT_DATA_USER_TOKEN_KEY, userToken)
                .putString(INPUT_DATA_URL_KEY, url)
                .putSerialized(INPUT_DATA_EVENT_KEY, event, Event.serializer())
                .build()
        }

        companion object {
            private const val INPUT_DATA_USER_ID_KEY = "userId"
            private const val INPUT_DATA_USER_TOKEN_KEY = "userToken"
            private const val INPUT_DATA_URL_KEY = "url"
            private const val INPUT_DATA_EVENT_KEY = "event"

            fun fromWorkerData(data: Data): Input {
                return Input(
                    url = data.getString(INPUT_DATA_URL_KEY) ?: error("Host is missing"),
                    event = data.getSerialized(INPUT_DATA_EVENT_KEY, Event.serializer()) ?: error("Event is missing"),
                    userId = data.getString(INPUT_DATA_USER_ID_KEY) ?: error("User ID is missing"),
                    userToken = data.getString(INPUT_DATA_USER_TOKEN_KEY) ?: error("User token is missing")
                )
            }
        }
    }

    companion object {
        private const val DEFAULT_MAX_ATTEMPTS = 10
    }
}

================
File: app/src/main/java/dev/paypass/data/event/EventMapper.kt
================
package dev.paypass.data.event

import dev.paypass.data.event.db.EventEntity
import dev.paypass.domain.Event

internal object EventMapper {

    fun domainToDatabase(event: Event): EventEntity {
        val type = when (event) {
            is Event.Sms -> EventEntity.Type.SMS
            is Event.Notification -> EventEntity.Type.NOTIFICATION
        }

        return EventEntity(
            id = event.id,
            type = type,
            timestamp = event.timestamp,
            author = when (event) {
                is Event.Sms -> event.phoneNumber
                is Event.Notification -> event.title
            },
            message = event.message,
            packageName = when (event) {
                is Event.Sms -> null
                is Event.Notification -> event.packageName
            },
            isProcessed = event.status is Event.Status.Processed,
            errorReasonType = when (event.status) {
                is Event.Status.Error.NoInternet -> EventEntity.ErrorReasonType.NO_INTERNET
                is Event.Status.Error.ServerError -> EventEntity.ErrorReasonType.SERVER_ERROR
                is Event.Status.Error.Unknown -> EventEntity.ErrorReasonType.UNKNOWN
                else -> null
            },
            errorReasonMessage = when (event.status) {
                is Event.Status.Error.ServerError -> (event.status as Event.Status.Error.ServerError).message
                is Event.Status.Error.Unknown -> (event.status as Event.Status.Error.Unknown).message
                else -> null
            }
        )
    }

    fun databaseToDomain(eventEntity: EventEntity): Event {
        val status = when {
            eventEntity.isProcessed -> Event.Status.Processed

            eventEntity.errorReasonType != null -> when (eventEntity.errorReasonType) {
                EventEntity.ErrorReasonType.NO_INTERNET -> Event.Status.Error.NoInternet

                EventEntity.ErrorReasonType.SERVER_ERROR -> Event.Status.Error.ServerError(
                    code = eventEntity.errorReasonServerCode ?: 0,
                    message = eventEntity.errorReasonMessage ?: ""
                )

                EventEntity.ErrorReasonType.UNKNOWN -> Event.Status.Error.Unknown(
                    message = eventEntity.errorReasonMessage ?: ""
                )
            }

            else -> Event.Status.New
        }

        return when (eventEntity.type) {
            EventEntity.Type.SMS -> Event.Sms(
                message = eventEntity.message,
                timestamp = eventEntity.timestamp,
                phoneNumber = eventEntity.author,
                status = status
            )

            EventEntity.Type.NOTIFICATION -> Event.Notification(
                message = eventEntity.message,
                timestamp = eventEntity.timestamp,
                title = eventEntity.author,
                packageName = eventEntity.packageName!!,
                status = status
            )
        }
    }
}

================
File: app/src/main/java/dev/paypass/data/event/EventRepositoryImpl.kt
================
package dev.paypass.data.event

import dev.paypass.data.AppDatabase
import dev.paypass.data.event.db.EventEntity
import dev.paypass.domain.Event
import dev.paypass.domain.event.EventRepository
import dev.paypass.data.network.BackendApi
import dev.paypass.data.network.event.EventRequestBody
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.scan
import timber.log.Timber

internal class EventRepositoryImpl(
    database: AppDatabase,
    private val backendApi: BackendApi
) : EventRepository {

    private val eventDao = database.eventDao()

    override fun notProcessedEvents(): Flow<List<Event>> {
        return eventDao.getNotProcessedEvents()
            .map { list -> list.map(EventMapper::databaseToDomain) }
            .scan(emptyList<Event>() to emptyList<Event>()) { (old), new ->
                // высчитываем какие стали новыми или изменились
                val changed = new.filterNot { old.contains(it) }
                new to changed
            }
            .map { (_, changed) -> changed }
            .filter { it.isNotEmpty() }
    }

    override suspend fun saveEvent(event: Event) {
        val databaseNotificationEvent = EventMapper.domainToDatabase(event)

        eventDao.insertEvent(databaseNotificationEvent)
    }

    override suspend fun sendEvent(
        userId: String,
        userToken: String,
        url: String,
        event: Event
    ): Result<Unit> {
        Timber.d("Sending event: $event")

        val request = EventRequestBody(
            phoneNumber = userId,
            userToken = userToken,
            packageName = when (event) {
                is Event.Notification -> event.packageName
                is Event.Sms -> "sms.messaging"
            },
            message = event.message
        )

        return try {
            val response = backendApi.sendEvent(request, url)

            if (response.isSuccessful) {
                Timber.d("Event sent successfully: $event")
                eventDao.updateEvent(event.id, isProcessed = true)

                Result.success(Unit)
            } else {
                val error = response.errorBody()?.string()

                Timber.e("Failed to send event: $error")

                eventDao.updateEvent(
                    id = event.id,
                    isProcessed = false,
                    errorReasonType = EventEntity.ErrorReasonType.SERVER_ERROR,
                )

                Result.failure(Exception("Failed to send event: $error"))
            }
        } catch (e: Exception) {
            Timber.e(e, "Failed to send event")

            eventDao.updateEvent(
                id = event.id,
                isProcessed = false,
                errorReasonType = EventEntity.ErrorReasonType.UNKNOWN,
            )

            Result.failure(e)
        }
    }
}

================
File: app/src/main/java/dev/paypass/data/keeplife/KeepLifeManagerImpl.kt
================
package dev.paypass.data.keeplife

import android.content.Context
import dev.paypass.KeepLifeService
import dev.paypass.domain.keeplife.KeepLifeManager

internal class KeepLifeManagerImpl(
    private val context: Context
) : KeepLifeManager {

    override fun start() {
        KeepLifeService.startService(context)
    }

    override fun stop() {
        KeepLifeService.stopService(context)
    }

    override fun isStarted(): Boolean {
        // TODO: Implement
        return false
    }
}

================
File: app/src/main/java/dev/paypass/data/network/config/ConfigRequestBody.kt
================
package dev.paypass.data.network.config

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ConfigRequestBody(
    @SerialName("phone_number")
    val phoneNumber: String,
    @SerialName("token")
    val token: String,
    @SerialName("AppVer")
    val pppVersion: String
)

================
File: app/src/main/java/dev/paypass/data/network/config/ConfigResponseBody.kt
================
package dev.paypass.data.network.config

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ConfigResponseBody(
    @SerialName("urlForPush")
    val urlForPush: String,
    @SerialName("urlForSms")
    val urlForSms: String,
)

================
File: app/src/main/java/dev/paypass/data/network/event/EventRequestBody.kt
================
package dev.paypass.data.network.event

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class EventRequestBody(
    @SerialName("phone_number")
    val phoneNumber: String,
    @SerialName("token")
    val userToken: String,
    @SerialName("app_name")
    val packageName: String,
    @SerialName("message")
    val message: String? = null,
)

================
File: app/src/main/java/dev/paypass/data/network/BackendApi.kt
================
package dev.paypass.data.network

import com.jakewharton.retrofit2.converter.kotlinx.serialization.asConverterFactory
import dev.paypass.data.network.config.ConfigRequestBody
import dev.paypass.data.network.config.ConfigResponseBody
import dev.paypass.data.network.event.EventRequestBody
import kotlinx.serialization.StringFormat
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import retrofit2.Response
import retrofit2.Retrofit
import retrofit2.create
import retrofit2.http.Body
import retrofit2.http.POST
import retrofit2.http.Url

interface BackendApi {

    @EncryptToField("data")
    @POST
    suspend fun sendEvent(
        @Body requestBody: EventRequestBody,
        @Url url: String
    ): Response<Unit>

    @POST("MobileAppSettings")
    suspend fun getConfig(
        @Body requestBody: ConfigRequestBody
    ): Response<ConfigResponseBody>

    companion object {

        fun create(
            okHttpClient: OkHttpClient,
            stringFormat: StringFormat,
            baseUrl: String,
        ): BackendApi {
            val contentType = "application/json".toMediaType()

            val retrofit = Retrofit.Builder()
                .client(okHttpClient)
                .baseUrl(baseUrl)
                .addConverterFactory(stringFormat.asConverterFactory(contentType))
                .build()

            return retrofit.create()
        }
    }
}

================
File: app/src/main/java/dev/paypass/data/network/EncryptionInterceptor.kt
================
package dev.paypass.data.network

import android.util.Base64
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.buildJsonObject
import okhttp3.Interceptor
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import okio.Buffer
import retrofit2.Invocation
import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec

class EncryptionInterceptor(
    secretKey: String,
    algorithm: String,
    private val cipherTransformation: String
) : Interceptor {

    private val secretKeySpec = SecretKeySpec(secretKey.toByteArray(), algorithm)

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()

        val invocation = request.tag(Invocation::class.java) ?: error("Invocation not found")

        val encryptToFieldAnnotation = invocation.method().getAnnotation(EncryptToField::class.java) ?: return chain.proceed(request)

        val rawBody = Buffer().use {
            request.body?.writeTo(it)
            it.readByteString().utf8()
        }

        val cipher = Cipher.getInstance(cipherTransformation)
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec)

        val encryptedBytes = cipher.doFinal(rawBody.toByteArray())
        val encryptedMessage = Base64.encodeToString(encryptedBytes, Base64.DEFAULT)

        val encryptedBody = buildJsonObject {
            put(encryptToFieldAnnotation.fieldName, JsonPrimitive(encryptedMessage))
        }.toString().toRequestBody("application/json".toMediaType())

        val newRequest = request.newBuilder()
            .addHeader("Content-Type", "application/json")
            .method(request.method, encryptedBody)
            .build()

        return chain.proceed(newRequest)
    }

}

================
File: app/src/main/java/dev/paypass/data/network/EncryptToField.kt
================
package dev.paypass.data.network

annotation class EncryptToField(
    val fieldName: String
)

================
File: app/src/main/java/dev/paypass/data/work/WorkDataExtensions.kt
================
package dev.paypass.data.work

import androidx.work.Data
import kotlinx.serialization.DeserializationStrategy
import kotlinx.serialization.SerializationStrategy
import kotlinx.serialization.json.Json

private val stringFormat = Json

internal fun <T> Data.Builder.putSerialized(key: String, value: T, serializationStrategy: SerializationStrategy<T>): Data.Builder {
    val string = stringFormat.encodeToString(serializationStrategy, value)
    putString(key, string)
    return this
}

internal fun <T> Data.getSerialized(key: String, serializationStrategy: DeserializationStrategy<T>): T? {
    val string = getString(key) ?: return null
    return stringFormat.decodeFromString(serializationStrategy, string)
}

================
File: app/src/main/java/dev/paypass/data/AppDatabase.kt
================
package dev.paypass.data

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.driver.bundled.BundledSQLiteDriver
import dev.paypass.data.event.db.EventDao
import dev.paypass.data.event.db.EventEntity
import kotlinx.coroutines.Dispatchers

@Database(
    entities = [EventEntity::class],
    version = AppDatabase.VERSION
)
abstract class AppDatabase: RoomDatabase() {

    abstract fun eventDao(): EventDao

    companion object {

        internal const val VERSION = 1

        fun create(
            context: Context,
            fileName: String,
            migrations: Array<Migration>? = null
        ): AppDatabase {
            val appContext = context.applicationContext
            val dbFile = appContext.getDatabasePath(fileName)

            val databaseBuilder = Room.databaseBuilder<AppDatabase>(
                context = appContext,
                name = dbFile.absolutePath
            )

            return databaseBuilder
                .addMigrations(*migrations.orEmpty())
                .fallbackToDestructiveMigrationOnDowngrade(dropAllTables = false)
                .setDriver(BundledSQLiteDriver())
                .setQueryCoroutineContext(Dispatchers.IO)
                .build()
        }
    }
}

================
File: app/src/main/java/dev/paypass/data/WorkInfoManagerImpl.kt
================
package dev.paypass.data

import androidx.work.WorkInfo
import androidx.work.WorkManager
import androidx.work.WorkQuery
import dev.paypass.domain.work.WorkInfo as AppWorkInfo
import dev.paypass.domain.work.WorkInfoManager
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

class WorkInfoManagerImpl(
    private val workManager: WorkManager
) : WorkInfoManager {

    override fun allWorksChanges(): Flow<List<AppWorkInfo>> {
        val query = WorkQuery.fromTags("send_event")

        return workManager.getWorkInfosFlow(query)
            .map { workInfos ->
                workInfos.map { workInfo ->
                    AppWorkInfo(
                        id = workInfo.id.toString(),
                        status = when (workInfo.state) {
                            WorkInfo.State.ENQUEUED -> AppWorkInfo.Status.ENQUEUED
                            WorkInfo.State.RUNNING -> AppWorkInfo.Status.RUNNING
                            WorkInfo.State.SUCCEEDED -> AppWorkInfo.Status.SUCCEEDED
                            WorkInfo.State.FAILED -> AppWorkInfo.Status.FAILED
                            WorkInfo.State.BLOCKED -> AppWorkInfo.Status.BLOCKED
                            WorkInfo.State.CANCELLED -> AppWorkInfo.Status.CANCELLED
                        }
                    )
                }
            }
    }
}

================
File: app/src/main/java/dev/paypass/di/module/AppModule.kt
================
package dev.paypass.di.module

import dev.paypass.data.config.work.LoadConfigSchedulerImpl
import dev.paypass.data.event.work.SendEventSchedulerImpl
import dev.paypass.data.AppDatabase
import dev.paypass.di.KoinComponentFactory
import dev.paypass.domain.config.LoadConfigScheduler
import dev.paypass.domain.event.SendEventScheduler
import dev.paypass.ui.ComponentFactory
import org.koin.core.module.dsl.new
import org.koin.core.module.dsl.singleOf
import org.koin.dsl.bind
import org.koin.dsl.module

val appModule = module {
    single<ComponentFactory> { new(::KoinComponentFactory) }
    single { AppDatabase.create(get(), "paypass.db") }

    singleOf(::LoadConfigSchedulerImpl).bind<LoadConfigScheduler>()
    singleOf(::SendEventSchedulerImpl).bind<SendEventScheduler>()
}

================
File: app/src/main/java/dev/paypass/di/module/ComponentModule.kt
================
package dev.paypass.di.module

import dev.paypass.ui.component.RootComponent
import dev.paypass.ui.component.RootComponentImpl
import dev.paypass.ui.component.app_conditions.AppConditionsScreenComponent
import dev.paypass.ui.component.app_conditions.AppConditionsScreenComponentImpl
import dev.paypass.ui.component.main.MainScreenComponent
import dev.paypass.ui.component.main.MainScreenComponentImpl
import org.koin.core.module.dsl.new
import org.koin.dsl.module

val componentModule = module {
    factory<RootComponent> { new(::RootComponentImpl) }
    factory<AppConditionsScreenComponent> { new(::AppConditionsScreenComponentImpl) }
    factory<MainScreenComponent> { new(::MainScreenComponentImpl) }
}

================
File: app/src/main/java/dev/paypass/di/module/DataModule.kt
================
package dev.paypass.di.module

import androidx.work.WorkManager
import dev.paypass.data.WorkInfoManagerImpl
import dev.paypass.data.auth.AuthInfoStorage
import dev.paypass.data.auth.AuthRepositoryImpl
import dev.paypass.data.config.RemoteConfigRepositoryImpl
import dev.paypass.data.config.RemoteConfigStorage
import dev.paypass.data.event.EventRepositoryImpl
import dev.paypass.data.keeplife.KeepLifeManagerImpl
import dev.paypass.domain.auth.AuthRepository
import dev.paypass.domain.config.RemoteConfigRepository
import dev.paypass.domain.event.EventRepository
import dev.paypass.domain.keeplife.KeepLifeManager
import dev.paypass.domain.work.WorkInfoManager
import org.koin.core.module.dsl.singleOf
import org.koin.dsl.bind
import org.koin.dsl.module

val dataModule = module {
    singleOf(::AuthInfoStorage)
    singleOf(::AuthRepositoryImpl).bind<AuthRepository>()

    singleOf(::RemoteConfigStorage)
    singleOf(::RemoteConfigRepositoryImpl).bind<RemoteConfigRepository>()

    singleOf(::KeepLifeManagerImpl).bind<KeepLifeManager>()

    singleOf(::EventRepositoryImpl).bind<EventRepository>()

    factory<WorkManager> {
        WorkManager.getInstance(get())
    }
    singleOf(::WorkInfoManagerImpl).bind<WorkInfoManager>()
}

================
File: app/src/main/java/dev/paypass/di/module/DomainModule.kt
================
package dev.paypass.di.module

import dev.paypass.domain.auth.use_case.AuthInfoChangesUseCase
import dev.paypass.domain.auth.use_case.AuthInfoChangesUseCaseImpl
import dev.paypass.domain.auth.use_case.AuthSavePhoneNumberUseCase
import dev.paypass.domain.auth.use_case.AuthSavePhoneNumberUseCaseImpl
import dev.paypass.domain.auth.use_case.AuthSaveTokenUseCase
import dev.paypass.domain.auth.use_case.AuthSaveTokenUseCaseImpl
import dev.paypass.domain.condition.GetAllAppConditionsUseCase
import dev.paypass.domain.condition.GetAllAppConditionsUseCaseImpl
import dev.paypass.domain.config.use_case.RemoteConfigChangesUseCase
import dev.paypass.domain.config.use_case.RemoteConfigChangesUseCaseImpl
import dev.paypass.domain.auth.use_case.AuthScanQrUseCase
import dev.paypass.domain.auth.use_case.AuthScanQrUseCaseImpl
import dev.paypass.domain.config.use_case.LoadConfigByAuthChangesUseCase
import dev.paypass.domain.config.use_case.LoadConfigByAuthChangesUseCaseImpl
import dev.paypass.domain.event.use_case.SaveSmsEventsUseCase
import dev.paypass.domain.event.use_case.SaveSmsEventsUseCaseImpl
import dev.paypass.domain.event.use_case.SaveSystemNotificationEventsUseCase
import dev.paypass.domain.event.use_case.SaveSystemNotificationEventsUseCaseImpl
import dev.paypass.domain.event.use_case.SendNotProcessedEventsUseCase
import dev.paypass.domain.event.use_case.SendNotProcessedEventsUseCaseImpl
import dev.paypass.domain.keeplife.StartKeepLifeUseCase
import dev.paypass.domain.keeplife.StartKeepLifeUseCaseImpl
import org.koin.core.module.dsl.new
import org.koin.dsl.module

val domainModule = module {
    factory<GetAllAppConditionsUseCase> { new(::GetAllAppConditionsUseCaseImpl) }

    factory<StartKeepLifeUseCase> { new(::StartKeepLifeUseCaseImpl) }

    factory<LoadConfigByAuthChangesUseCase> { new(::LoadConfigByAuthChangesUseCaseImpl) }
    factory<RemoteConfigChangesUseCase> { new(::RemoteConfigChangesUseCaseImpl) }

    factory<AuthScanQrUseCase> { new(::AuthScanQrUseCaseImpl) }

    factory<AuthInfoChangesUseCase> { new(::AuthInfoChangesUseCaseImpl) }
    factory<AuthSavePhoneNumberUseCase> { new(::AuthSavePhoneNumberUseCaseImpl) }
    factory<AuthSaveTokenUseCase> { new(::AuthSaveTokenUseCaseImpl) }

    factory<SaveSmsEventsUseCase> { new(::SaveSmsEventsUseCaseImpl) }
    factory<SaveSystemNotificationEventsUseCase> { new(::SaveSystemNotificationEventsUseCaseImpl) }
    factory<SendNotProcessedEventsUseCase> { new(::SendNotProcessedEventsUseCaseImpl) }
}

================
File: app/src/main/java/dev/paypass/di/module/NetworkModule.kt
================
package dev.paypass.di.module

import dev.paypass.data.network.BackendApi
import dev.paypass.data.network.EncryptionInterceptor
import kotlinx.serialization.json.Json
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import org.koin.dsl.module
import kotlin.time.Duration.Companion.seconds
import kotlin.time.toJavaDuration

val networkModule = module {
    single<OkHttpClient> {
        val loggingInterceptor = HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }

        val encryptionInterceptor = EncryptionInterceptor(
            secretKey = ENCRYPTION_SECRET_KEY,
            algorithm = ENCRYPTION_ALGORITHM,
            cipherTransformation = ENCRYPTION_CIPHER_TRANSFORMATION
        )

        OkHttpClient.Builder()
            .addInterceptor(encryptionInterceptor)
            .addInterceptor(loggingInterceptor)
            .connectTimeout(20.seconds.toJavaDuration())
            .readTimeout(20.seconds.toJavaDuration())
            .writeTimeout(20.seconds.toJavaDuration())
            .build()
    }
    single<BackendApi> {
        val json = Json {
            ignoreUnknownKeys = true
        }

        BackendApi.create(
            okHttpClient = get(),
            baseUrl = BASE_URL,
            stringFormat = json
        )
    }
}

private const val BASE_URL = "https://dev.pay-pass.pro/api/"
private const val ENCRYPTION_SECRET_KEY = "9ixU}4zH>BbDX4Ta"
private const val ENCRYPTION_ALGORITHM = "AES"
private const val ENCRYPTION_CIPHER_TRANSFORMATION = "AES/ECB/PKCS5Padding"

================
File: app/src/main/java/dev/paypass/di/module/SystemModule.kt
================
package dev.paypass.di.module

import dev.paypass.system.activity.CurrentActivityProvider
import dev.paypass.system.activity.CurrentActivityProviderImpl
import dev.paypass.system.permission.PermissionsManager
import dev.paypass.system.permission.PermissionsManagerImpl
import dev.paypass.system.notification.SystemNotificationsManager
import dev.paypass.system.notification.SystemNotificationsManagerImpl
import dev.paypass.system.sms.SmsManager
import dev.paypass.system.sms.SmsManagerImpl
import dev.paypass.system.storage.StorageFactoryProvider
import dev.paypass.system.storage.impl.StorageFactoryProviderImpl
import dev.paypass.system.thirdparty.QrScanLauncher
import dev.paypass.system.thirdparty.QrScanLauncherImpl
import org.koin.core.module.dsl.new
import org.koin.core.module.dsl.singleOf
import org.koin.dsl.bind
import org.koin.dsl.module

val systemModule = module {
    single<CurrentActivityProvider>(createdAtStart = true) { new(::CurrentActivityProviderImpl) }
    single<SmsManager>(createdAtStart = true) { new(::SmsManagerImpl) }
    single<SystemNotificationsManager>(createdAtStart = true) { new(::SystemNotificationsManagerImpl) }
    single<PermissionsManager> { new(::PermissionsManagerImpl) }

    singleOf(::StorageFactoryProviderImpl).bind<StorageFactoryProvider>()

    singleOf(::QrScanLauncherImpl).bind<QrScanLauncher>()
}

================
File: app/src/main/java/dev/paypass/di/KoinComponentFactory.kt
================
package dev.paypass.di

import com.arkivanov.decompose.ComponentContext
import dev.paypass.ui.ComponentFactory
import org.koin.core.parameter.parametersOf
import org.koin.mp.KoinPlatform
import kotlin.reflect.KClass

class KoinComponentFactory : ComponentFactory {

    override fun <T : Any> createComponent(
        context: ComponentContext,
        componentClass: KClass<T>,
        params: Any?
    ): T {
        return KoinPlatform.getKoin().get(componentClass) {
            parametersOf(context, params)
        }
    }
}

================
File: app/src/main/java/dev/paypass/domain/auth/use_case/AuthInfoChangesUseCase.kt
================
package dev.paypass.domain.auth.use_case

import dev.paypass.domain.auth.AuthInfo
import kotlinx.coroutines.flow.Flow

interface AuthInfoChangesUseCase {

    operator fun invoke(): Flow<AuthInfo>
}

================
File: app/src/main/java/dev/paypass/domain/auth/use_case/AuthInfoChangesUseCaseImpl.kt
================
package dev.paypass.domain.auth.use_case

import dev.paypass.domain.auth.AuthInfo
import dev.paypass.domain.auth.AuthRepository
import kotlinx.coroutines.flow.Flow

internal class AuthInfoChangesUseCaseImpl(
    private val authRepository: AuthRepository
) : AuthInfoChangesUseCase {

    override fun invoke(): Flow<AuthInfo> {
        return authRepository.authInfoChanges()
    }
}

================
File: app/src/main/java/dev/paypass/domain/auth/use_case/AuthSavePhoneNumberUseCase.kt
================
package dev.paypass.domain.auth.use_case

interface AuthSavePhoneNumberUseCase {

    suspend operator fun invoke(phoneNumber: String)
}

================
File: app/src/main/java/dev/paypass/domain/auth/use_case/AuthSavePhoneNumberUseCaseImpl.kt
================
package dev.paypass.domain.auth.use_case

import dev.paypass.domain.auth.AuthRepository

internal class AuthSavePhoneNumberUseCaseImpl(
    private val authRepository: AuthRepository
) : AuthSavePhoneNumberUseCase {

    override suspend fun invoke(phoneNumber: String) {
        authRepository.savePhoneNumber(phoneNumber)
    }
}

================
File: app/src/main/java/dev/paypass/domain/auth/use_case/AuthSaveTokenUseCase.kt
================
package dev.paypass.domain.auth.use_case

interface AuthSaveTokenUseCase {

    suspend operator fun invoke(token: String)
}

================
File: app/src/main/java/dev/paypass/domain/auth/use_case/AuthSaveTokenUseCaseImpl.kt
================
package dev.paypass.domain.auth.use_case

import dev.paypass.domain.auth.AuthRepository

class AuthSaveTokenUseCaseImpl(
    private val authRepository: AuthRepository
) : AuthSaveTokenUseCase {

    override suspend fun invoke(token: String) {
        authRepository.saveToken(token)
    }
}

================
File: app/src/main/java/dev/paypass/domain/auth/use_case/AuthScanQrResult.kt
================
package dev.paypass.domain.auth.use_case

sealed interface AuthScanQrResult {

    data class Token(val token: String) : AuthScanQrResult

    data object UserCanceled : AuthScanQrResult

    data class MissingPermission(
        val isPermissionPermanentlyDenied: Boolean
    ) : AuthScanQrResult
}

================
File: app/src/main/java/dev/paypass/domain/auth/use_case/AuthScanQrUseCase.kt
================
package dev.paypass.domain.auth.use_case

interface AuthScanQrUseCase {

    suspend operator fun invoke(): AuthScanQrResult
}

================
File: app/src/main/java/dev/paypass/domain/auth/use_case/AuthScanQrUseCaseImpl.kt
================
package dev.paypass.domain.auth.use_case

import dev.paypass.system.permission.Permission
import dev.paypass.system.permission.PermissionsManager
import dev.paypass.system.thirdparty.QrScanLauncher
import dev.paypass.system.thirdparty.QrScanResult

internal class AuthScanQrUseCaseImpl(
    private val permissionsManager: PermissionsManager,
    private val qrScanLauncher: QrScanLauncher
) : AuthScanQrUseCase {

    override suspend fun invoke(): AuthScanQrResult {
        val cameraPermissionStatue = permissionsManager.requestPermission(Permission.CAMERA)

        return if (cameraPermissionStatue.isGranted.not()) {
            AuthScanQrResult.MissingPermission(
                isPermissionPermanentlyDenied = cameraPermissionStatue.isDeniedRationale
            )
        } else {
            when (val qrResult = qrScanLauncher.launchQrScan()) {
                is QrScanResult.Success -> AuthScanQrResult.Token(
                    token = qrResult.rawValue
                )

                QrScanResult.UserCanceled -> AuthScanQrResult.UserCanceled

                QrScanResult.MissingPermission -> AuthScanQrResult.MissingPermission(
                    isPermissionPermanentlyDenied = true
                )

                is QrScanResult.Error -> error("Unexpected error: ${qrResult.exception}")
            }
        }
    }
}

================
File: app/src/main/java/dev/paypass/domain/auth/AuthInfo.kt
================
package dev.paypass.domain.auth

import kotlinx.serialization.Serializable

@Serializable
data class AuthInfo(
    val id: String,
    val token: String
)

================
File: app/src/main/java/dev/paypass/domain/auth/AuthRepository.kt
================
package dev.paypass.domain.auth

import kotlinx.coroutines.flow.Flow

interface AuthRepository {

    fun authInfoChanges(): Flow<AuthInfo>

    suspend fun savePhoneNumber(phoneNumber: String)

    suspend fun saveToken(token: String)
}

================
File: app/src/main/java/dev/paypass/domain/condition/AppCondition.kt
================
package dev.paypass.domain.condition

sealed interface AppCondition {

    enum class Permission : AppCondition {
        READ_SMS,
        RECEIVE_SMS,
        POST_NOTIFICATIONS
    }

    data object EnableNotificationListener : AppCondition
}

================
File: app/src/main/java/dev/paypass/domain/condition/GetAllAppConditionsUseCase.kt
================
package dev.paypass.domain.condition

interface GetAllAppConditionsUseCase {
    operator fun invoke(): Map<AppCondition, Boolean>
}

================
File: app/src/main/java/dev/paypass/domain/condition/GetAllAppConditionsUseCaseImpl.kt
================
package dev.paypass.domain.condition

import dev.paypass.system.notification.SystemNotificationsManager
import dev.paypass.system.permission.Permission
import dev.paypass.system.permission.PermissionsManager

class GetAllAppConditionsUseCaseImpl(
    private val permissionsManager: PermissionsManager,
    private val systemNotificationsManager: SystemNotificationsManager,
) : GetAllAppConditionsUseCase {

    override fun invoke(): Map<AppCondition, Boolean> {
        return buildMap {
            put(AppCondition.Permission.READ_SMS, permissionsManager.checkPermission(Permission.READ_SMS).isGranted)
            put(AppCondition.Permission.RECEIVE_SMS, permissionsManager.checkPermission(Permission.RECEIVE_SMS).isGranted)

            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
                put(AppCondition.Permission.POST_NOTIFICATIONS, permissionsManager.checkPermission(Permission.POST_NOTIFICATIONS).isGranted)
            }

            put(AppCondition.EnableNotificationListener, systemNotificationsManager.isNotificationListenerEnabled())
        }
    }
}

================
File: app/src/main/java/dev/paypass/domain/config/use_case/LoadConfigByAuthChangesUseCase.kt
================
package dev.paypass.domain.config.use_case

interface LoadConfigByAuthChangesUseCase {

    suspend operator fun invoke()
}

================
File: app/src/main/java/dev/paypass/domain/config/use_case/LoadConfigByAuthChangesUseCaseImpl.kt
================
package dev.paypass.domain.config.use_case

import dev.paypass.domain.auth.AuthRepository
import dev.paypass.domain.config.LoadConfigScheduler
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter

internal class LoadConfigByAuthChangesUseCaseImpl(
    private val authRepository: AuthRepository,
    private val loadConfigScheduler: LoadConfigScheduler,
) : LoadConfigByAuthChangesUseCase {

    override suspend fun invoke() {
        authRepository.authInfoChanges()
            .filter { authInfo ->
                authInfo.id.isNotEmpty() && authInfo.token.isNotEmpty()
            }
            .distinctUntilChanged()
            .collect { authInfo ->
                loadConfigScheduler.schedule(authInfo)
            }
    }
}

================
File: app/src/main/java/dev/paypass/domain/config/use_case/RemoteConfigChangesUseCase.kt
================
package dev.paypass.domain.config.use_case

import dev.paypass.domain.config.RemoteConfig
import kotlinx.coroutines.flow.Flow

interface RemoteConfigChangesUseCase {
    operator fun invoke(): Flow<RemoteConfig>
}

================
File: app/src/main/java/dev/paypass/domain/config/use_case/RemoteConfigChangesUseCaseImpl.kt
================
package dev.paypass.domain.config.use_case

import dev.paypass.domain.config.RemoteConfig
import dev.paypass.domain.config.RemoteConfigRepository
import kotlinx.coroutines.flow.Flow

internal class RemoteConfigChangesUseCaseImpl(
    private val remoteConfigRepository: RemoteConfigRepository
) : RemoteConfigChangesUseCase {

    override fun invoke(): Flow<RemoteConfig> {
        return remoteConfigRepository.remoteConfigChanges()
    }
}

================
File: app/src/main/java/dev/paypass/domain/config/LoadConfigScheduler.kt
================
package dev.paypass.domain.config

import dev.paypass.domain.auth.AuthInfo

interface LoadConfigScheduler {

    fun schedule(authInfo: AuthInfo)
}

================
File: app/src/main/java/dev/paypass/domain/config/RemoteConfig.kt
================
package dev.paypass.domain.config

import kotlinx.serialization.Serializable

@Serializable
data class RemoteConfig(
    val urlForPush: String,
    val urlForSms: String,
    val urlForHeartBeat: String,
)

================
File: app/src/main/java/dev/paypass/domain/config/RemoteConfigRepository.kt
================
package dev.paypass.domain.config

import kotlinx.coroutines.flow.Flow

interface RemoteConfigRepository {

    fun remoteConfigChanges(): Flow<RemoteConfig>

    suspend fun loadRemoteConfig(
        phoneNumber: String,
        token: String,
        appVersion: String
    ): Result<RemoteConfig>
}

================
File: app/src/main/java/dev/paypass/domain/event/use_case/SaveSmsEventsUseCase.kt
================
package dev.paypass.domain.event.use_case

interface SaveSmsEventsUseCase {

    suspend operator fun invoke()
}

================
File: app/src/main/java/dev/paypass/domain/event/use_case/SaveSmsEventsUseCaseImpl.kt
================
package dev.paypass.domain.event.use_case

import dev.paypass.domain.Event
import dev.paypass.domain.event.EventRepository
import dev.paypass.system.sms.SmsManager
import timber.log.Timber

internal class SaveSmsEventsUseCaseImpl(
    private val smsManager: SmsManager,
    private val eventRepository: EventRepository
) : SaveSmsEventsUseCase {

    override suspend fun invoke() {
        smsManager.incomingSms
            .collect { incomingSms ->
                val event = Event.Sms(
                    message = incomingSms.message,
                    timestamp = incomingSms.timestamp,
                    phoneNumber = incomingSms.sender,
                    status = Event.Status.New
                )

                Timber.d("SaveSmsEventsUseCaseImpl event: $event")

                eventRepository.saveEvent(event)
            }
    }
}

================
File: app/src/main/java/dev/paypass/domain/event/use_case/SaveSystemNotificationEventsUseCase.kt
================
package dev.paypass.domain.event.use_case

interface SaveSystemNotificationEventsUseCase {

    suspend operator fun invoke()
}

================
File: app/src/main/java/dev/paypass/domain/event/use_case/SaveSystemNotificationEventsUseCaseImpl.kt
================
package dev.paypass.domain.event.use_case

import dev.paypass.domain.Event
import dev.paypass.domain.event.EventRepository
import dev.paypass.system.notification.SystemNotificationsManager
import kotlinx.coroutines.flow.distinctUntilChanged
import timber.log.Timber

class SaveSystemNotificationEventsUseCaseImpl(
    private val systemNotificationsManager: SystemNotificationsManager,
    private val eventRepository: EventRepository
) : SaveSystemNotificationEventsUseCase {

    override suspend fun invoke() {
        systemNotificationsManager.postedNotifications
            .distinctUntilChanged()
            .collect { systemNotification ->
                val event = Event.Notification(
                    message = systemNotification.text,
                    timestamp = systemNotification.timestamp,
                    title = systemNotification.title,
                    packageName = systemNotification.packageName,
                    status = Event.Status.New
                )

                Timber.d("SaveSystemNotificationEventsUseCaseImpl event: $event")

                eventRepository.saveEvent(event)
            }
    }
}

================
File: app/src/main/java/dev/paypass/domain/event/use_case/SendNotProcessedEventsUseCase.kt
================
package dev.paypass.domain.event.use_case

interface SendNotProcessedEventsUseCase {

    suspend operator fun invoke()
}

================
File: app/src/main/java/dev/paypass/domain/event/use_case/SendNotProcessedEventsUseCaseImpl.kt
================
package dev.paypass.domain.event.use_case

import dev.paypass.domain.Event
import dev.paypass.domain.auth.AuthInfo
import dev.paypass.domain.auth.AuthRepository
import dev.paypass.domain.config.RemoteConfig
import dev.paypass.domain.config.RemoteConfigRepository
import dev.paypass.domain.event.EventRepository
import dev.paypass.domain.event.SendEventScheduler
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.scan

internal class SendNotProcessedEventsUseCaseImpl(
    private val authRepository: AuthRepository,
    private val configRepository: RemoteConfigRepository,
    private val eventRepository: EventRepository,
    private val sendEventScheduler: SendEventScheduler,
) : SendNotProcessedEventsUseCase {

    override suspend fun invoke() {
        combine(
            authRepository.authInfoChanges(),
            configRepository.remoteConfigChanges(),
            eventRepository.notProcessedEvents()
        ) { authInfo, config, events ->
            Triple(authInfo, config, events)
        }
            .scan<Triple<AuthInfo, RemoteConfig, List<Event>>, Triple<AuthInfo, RemoteConfig, List<Event>>?>(null) { old, current ->
                val (authInfo, config, events) = current

                val authChanged = old?.first?.let { it != authInfo } ?: false
                val configChanged = old?.second?.let { it != config } ?: false
                val oldEvents = old?.third ?: emptyList()
                val newOrChangedEvents = events.filterNot(oldEvents::contains)

                // Если auth или config поменялись, или появились новые/изменённые эвенты
                if (authChanged || configChanged || newOrChangedEvents.isNotEmpty()) {
                    events.forEach { event ->
                        sendEventScheduler.schedule(
                            userId = authInfo.id,
                            userToken = authInfo.token,
                            url = when (event) {
                                is Event.Notification -> config.urlForPush
                                is Event.Sms -> config.urlForSms
                            },
                            event = event
                        )
                    }
                }

                current // Возвращаем текущие значения, чтобы в след. итерации они стали "старым" состоянием
            }
            .collect()
    }
}

================
File: app/src/main/java/dev/paypass/domain/event/EventRepository.kt
================
package dev.paypass.domain.event

import dev.paypass.domain.Event
import kotlinx.coroutines.flow.Flow

interface EventRepository {

    fun notProcessedEvents(): Flow<List<Event>>

    suspend fun saveEvent(event: Event)

    suspend fun sendEvent(
        userId: String,
        userToken: String,
        url: String,
        event: Event
    ): Result<Unit>
}

================
File: app/src/main/java/dev/paypass/domain/event/SendEventScheduler.kt
================
package dev.paypass.domain.event

import dev.paypass.domain.Event

interface SendEventScheduler {

    fun schedule(
        userId: String,
        userToken: String,
        url: String,
        event: Event,
    )
}

================
File: app/src/main/java/dev/paypass/domain/keeplife/KeepLifeManager.kt
================
package dev.paypass.domain.keeplife

interface KeepLifeManager {

    fun start()

    fun stop()

    fun isStarted(): Boolean
}

================
File: app/src/main/java/dev/paypass/domain/keeplife/StartKeepLifeUseCase.kt
================
package dev.paypass.domain.keeplife

interface StartKeepLifeUseCase {
    suspend operator fun invoke()
}

================
File: app/src/main/java/dev/paypass/domain/keeplife/StartKeepLifeUseCaseImpl.kt
================
package dev.paypass.domain.keeplife

import android.os.Build
import com.arkivanov.essenty.lifecycle.Lifecycle
import dev.paypass.system.activity.CurrentActivityProvider
import dev.paypass.system.permission.Permission
import dev.paypass.system.permission.PermissionsManager
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.first

internal class StartKeepLifeUseCaseImpl(
    private val currentActivityProvider: CurrentActivityProvider,
    private val permissionsManager: PermissionsManager,
    private val keepLifeManager: KeepLifeManager
) : StartKeepLifeUseCase {

    override suspend fun invoke() {
        waitGrantNotificationPermission()
        keepLifeManager.start()
    }

    private suspend fun waitGrantNotificationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            while (permissionsManager.checkPermission(Permission.POST_NOTIFICATIONS).isGranted.not()) {
                currentActivityProvider.lifecycle.stateFlow().first { it == Lifecycle.State.RESUMED }
            }
        }
    }

    private fun Lifecycle.stateFlow() = callbackFlow {
        val callbacks = object : Lifecycle.Callbacks {
            override fun onCreate() {
                trySend(Lifecycle.State.CREATED)
            }

            override fun onStart() {
                trySend(Lifecycle.State.STARTED)
            }

            override fun onResume() {
                trySend(Lifecycle.State.RESUMED)
            }

            override fun onPause() {
                trySend(Lifecycle.State.STARTED)
            }

            override fun onStop() {
                trySend(Lifecycle.State.CREATED)
            }

            override fun onDestroy() {
                trySend(Lifecycle.State.DESTROYED)
            }
        }

        subscribe(callbacks)

        awaitClose { unsubscribe(callbacks) }
    }
}

================
File: app/src/main/java/dev/paypass/domain/startup/StartupUseCase.kt
================
package dev.paypass.domain.startup

import android.content.Context

interface StartupUseCase {

    operator fun invoke(context: Context)

    companion object {
        fun newInstance(): StartupUseCase = StartupUseCaseImpl()
    }
}

================
File: app/src/main/java/dev/paypass/domain/startup/StartupUseCaseImpl.kt
================
package dev.paypass.domain.startup

import android.content.Context
import dev.paypass.di.module.appModule
import dev.paypass.di.module.componentModule
import dev.paypass.di.module.dataModule
import dev.paypass.di.module.domainModule
import dev.paypass.di.module.networkModule
import dev.paypass.di.module.systemModule
import dev.paypass.domain.config.use_case.LoadConfigByAuthChangesUseCase
import dev.paypass.domain.event.use_case.SaveSmsEventsUseCase
import dev.paypass.domain.event.use_case.SaveSystemNotificationEventsUseCase
import dev.paypass.domain.event.use_case.SendNotProcessedEventsUseCase
import dev.paypass.domain.keeplife.StartKeepLifeUseCase
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.koin.android.ext.koin.androidContext
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import org.koin.core.context.GlobalContext
import timber.log.Timber

internal class StartupUseCaseImpl : StartupUseCase, KoinComponent {

    private val scope = CoroutineScope(Dispatchers.Default)

    private val startKeepLifeUseCase: StartKeepLifeUseCase by inject()
    private val loadConfigByAuthChangesUseCase: LoadConfigByAuthChangesUseCase by inject()
    private val saveSmsEventsUseCase: SaveSmsEventsUseCase by inject()
    private val saveSystemNotificationEventsUseCase: SaveSystemNotificationEventsUseCase by inject()
    private val sendNotProcessedEventsUseCase: SendNotProcessedEventsUseCase by inject()

    override fun invoke(context: Context) {
        if (startKoinIfNotStarted(context)) {
            Timber.plant(Timber.DebugTree())

            scope.launch { startKeepLifeUseCase() }
            scope.launch { loadConfigByAuthChangesUseCase() }
            scope.launch { saveSmsEventsUseCase() }
            scope.launch { saveSystemNotificationEventsUseCase() }
            scope.launch { sendNotProcessedEventsUseCase() }

            Timber.d("All app components started")
        }
    }

    private fun startKoinIfNotStarted(context: Context): Boolean {
        if (GlobalContext.getOrNull() == null) {
            GlobalContext.startKoin {
                androidContext(context)

                modules(
                    appModule,
                    systemModule,
                    networkModule,
                    dataModule,
                    domainModule,
                    componentModule
                )
            }.koin

            return true
        }

        return false
    }
}

================
File: app/src/main/java/dev/paypass/domain/work/WorkInfo.kt
================
package dev.paypass.domain.work

data class WorkInfo(
    val id: String,
    val status: Status
) {

    enum class Status {
        ENQUEUED,
        RUNNING,
        SUCCEEDED,
        FAILED,
        BLOCKED,
        CANCELLED
    }
}

================
File: app/src/main/java/dev/paypass/domain/work/WorkInfoManager.kt
================
package dev.paypass.domain.work

import kotlinx.coroutines.flow.Flow

interface WorkInfoManager {

    fun allWorksChanges(): Flow<List<WorkInfo>>
}

================
File: app/src/main/java/dev/paypass/domain/Event.kt
================
package dev.paypass.domain

import kotlinx.serialization.Serializable

@Serializable
sealed interface Event {

    val id: Long
    val message: String
    val timestamp: Long
    val status: Status

    @Serializable
    data class Sms(
        override val id: Long = 0L,
        override val message: String,
        override val timestamp: Long,
        override val status: Status,
        val phoneNumber: String,
    ) : Event

    @Serializable
    data class Notification(
        override val id: Long = 0L,
        override val message: String,
        override val timestamp: Long,
        override val status: Status,
        val title: String,
        val packageName: String,
    ) : Event

    @Serializable
    sealed interface Status {

        @Serializable
        data object New : Status

        @Serializable
        data object Processed : Status

        @Serializable
        sealed interface Error : Status {

            @Serializable
            data object NoInternet : Error

            @Serializable
            data class ServerError(
                val code: Int,
                val message: String
            ) : Error

            @Serializable
            data class Unknown(
                val message: String
            ) : Error
        }
    }
}

================
File: app/src/main/java/dev/paypass/system/activity/CurrentActivityProvider.kt
================
package dev.paypass.system.activity

import androidx.activity.ComponentActivity
import com.arkivanov.essenty.lifecycle.Lifecycle

interface CurrentActivityProvider {

    val lifecycle: Lifecycle

    val currentActivity: ComponentActivity?
}

================
File: app/src/main/java/dev/paypass/system/activity/CurrentActivityProviderImpl.kt
================
package dev.paypass.system.activity

import android.app.Activity
import android.app.Application
import android.content.Context
import androidx.activity.ComponentActivity
import com.arkivanov.essenty.lifecycle.Lifecycle
import com.arkivanov.essenty.lifecycle.LifecycleRegistry
import com.arkivanov.essenty.lifecycle.pause
import com.arkivanov.essenty.lifecycle.resume
import com.arkivanov.essenty.lifecycle.start
import dev.paypass.system.util.DefaultActivityLifecycleCallbacks
import java.lang.ref.WeakReference

internal class CurrentActivityProviderImpl(
    context: Context
) : CurrentActivityProvider {

    private var _currentActivity = WeakReference<ComponentActivity>(null)

    private val lifecycleRegistry = LifecycleRegistry()
    override val lifecycle = lifecycleRegistry

    override val currentActivity
        get() = _currentActivity.get()

    private val callbacks = object : DefaultActivityLifecycleCallbacks {

        override fun onActivityStarted(activity: Activity) {
            (activity as? ComponentActivity)?.let {
                _currentActivity = WeakReference(it)
            }
        }

        override fun onActivityPostStarted(activity: Activity) {
            lifecycleRegistry.start()
        }

        override fun onActivityResumed(activity: Activity) {
            (activity as? ComponentActivity)?.let {
                _currentActivity = WeakReference(it)
            }
        }

        override fun onActivityPostResumed(activity: Activity) {
            lifecycleRegistry.resume()
        }

        override fun onActivityPostPaused(activity: Activity) {
            lifecycleRegistry.pause()
        }
    }

    init {
        val app = context.applicationContext as? Application
            ?: error("Application context not found")

        app.registerActivityLifecycleCallbacks(callbacks)
    }
}

================
File: app/src/main/java/dev/paypass/system/notification/AppNotificationListenerService.kt
================
package dev.paypass.system.notification

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.IBinder
import android.service.notification.NotificationListenerService
import android.service.notification.StatusBarNotification
import timber.log.Timber

class AppNotificationListenerService : NotificationListenerService() {

    private val componentName
        get() = ComponentName(applicationContext, AppNotificationListenerService::class.java)

    override fun onNotificationPosted(sbn: StatusBarNotification) {
        Timber.d("onNotificationPosted: $sbn")

        val notification = ReceivedNotification(
            packageName = sbn.packageName,
            title = sbn.notification.extras.getString("android.title", ""),
            text = sbn.notification.extras.getString("android.text", ""),
            timestamp = sbn.postTime
        )

        notificationReceiver?.onNotificationReceived(notification)
    }

    override fun onListenerConnected() {
        Timber.d("onListenerConnected")
    }

    override fun onListenerDisconnected() {
        Timber.d("onListenerDisconnected")
        tryReconnectService()
    }

    private fun tryReconnectService() {
        toggleNotificationListenerService()
        requestRebind(componentName)
    }

    private fun toggleNotificationListenerService() {
        packageManager.setComponentEnabledSetting(
            componentName,
            PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
            PackageManager.DONT_KILL_APP
        )

        packageManager.setComponentEnabledSetting(
            componentName,
            PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
            PackageManager.DONT_KILL_APP
        )
    }

    override fun onBind(intent: Intent?): IBinder? {
        return super.onBind(intent)
    }

    interface NotificationReceiver {

        fun onNotificationReceived(notification: ReceivedNotification)
    }

    companion object {
        var notificationReceiver: NotificationReceiver? = null

        fun startService(context: Context) {
            val intent = Intent(context, AppNotificationListenerService::class.java)
            context.startService(intent)
        }
    }
}

================
File: app/src/main/java/dev/paypass/system/notification/ReceivedNotification.kt
================
package dev.paypass.system.notification

data class ReceivedNotification(
    val packageName: String,
    val title: String,
    val text: String,
    val timestamp: Long
)

================
File: app/src/main/java/dev/paypass/system/notification/SystemNotificationsManager.kt
================
package dev.paypass.system.notification

import kotlinx.coroutines.flow.SharedFlow

interface SystemNotificationsManager {

    val postedNotifications: SharedFlow<ReceivedNotification>

    fun openNotificationListenerSettings()

    fun isNotificationListenerEnabled(): Boolean
}

================
File: app/src/main/java/dev/paypass/system/notification/SystemNotificationsManagerImpl.kt
================
package dev.paypass.system.notification

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.provider.Settings
import com.arkivanov.essenty.lifecycle.doOnResume
import dev.paypass.system.activity.CurrentActivityProvider
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import timber.log.Timber

internal class SystemNotificationsManagerImpl(
    private val context: Context,
    private val currentActivityProvider: CurrentActivityProvider,
) : SystemNotificationsManager {

    private val _postedNotifications = MutableSharedFlow<ReceivedNotification>()
    override val postedNotifications = _postedNotifications.asSharedFlow()

    private var isNotificationListenerStarted = false

    private val notificationReceiver = object : AppNotificationListenerService.NotificationReceiver {
        override fun onNotificationReceived(notification: ReceivedNotification) {
            Timber.d("onNotificationReceived: $notification")
            GlobalScope.launch { _postedNotifications.emit(notification) }
        }
    }

    init {
        currentActivityProvider.lifecycle.doOnResume { startNotificationListenerService() }
    }

    override fun openNotificationListenerSettings() {
        val intent = Intent(Settings.ACTION_NOTIFICATION_LISTENER_SETTINGS)

        currentActivityProvider.currentActivity?.startActivity(intent)
    }

    override fun isNotificationListenerEnabled(): Boolean {
        val enabledNotificationListeners = Settings.Secure.getString(
            context.contentResolver,
            ENABLED_NOTIFICATION_LISTENERS
        ).orEmpty()

        enabledNotificationListeners.split(":".toRegex())
            .dropLastWhile { it.isEmpty() }
            .forEach { enabledNotificationListenerComponentName ->
                val enabledNotificationListenerComponent = ComponentName.unflattenFromString(enabledNotificationListenerComponentName)

                if (context.packageName == enabledNotificationListenerComponent?.packageName.orEmpty()) {
                    // We have found that the user has enabled the notification listener of this app
                    return true
                }
            }

        return false
    }

    private fun startNotificationListenerService() {
        if (!isNotificationListenerStarted && isNotificationListenerEnabled()) {
            try {
                AppNotificationListenerService.notificationReceiver = notificationReceiver
                AppNotificationListenerService.startService(context)
                isNotificationListenerStarted = true
            } catch (e: Exception) {
                Timber.e(e, "Failed to start AppNotificationListenerService")
            }
        }
    }

    companion object {
        private const val ENABLED_NOTIFICATION_LISTENERS = "enabled_notification_listeners"
    }
}

================
File: app/src/main/java/dev/paypass/system/permission/Permission.kt
================
package dev.paypass.system.permission

import android.Manifest
import android.os.Build
import androidx.annotation.RequiresApi

enum class Permission(
    internal val androidPermission: String,
) {
    READ_SMS(Manifest.permission.READ_SMS),
    RECEIVE_SMS(Manifest.permission.RECEIVE_SMS),
    CAMERA(Manifest.permission.CAMERA),
    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    POST_NOTIFICATIONS(Manifest.permission.POST_NOTIFICATIONS),
}

================
File: app/src/main/java/dev/paypass/system/permission/PermissionsManager.kt
================
package dev.paypass.system.permission

interface PermissionsManager {

    suspend fun requestPermission(permission: Permission): PermissionStatus

    suspend fun requestPermissions(permissions: List<Permission>): Map<Permission, PermissionStatus>

    fun checkPermission(permission: Permission): PermissionStatus

    fun checkPermissions(permissions: List<Permission>): Map<Permission, PermissionStatus>

    fun openPermissionSettings()
}

================
File: app/src/main/java/dev/paypass/system/permission/PermissionsManagerImpl.kt
================
package dev.paypass.system.permission

import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.provider.Settings
import androidx.activity.result.contract.ActivityResultContracts
import dev.paypass.system.activity.CurrentActivityProvider
import kotlinx.coroutines.suspendCancellableCoroutine
import java.util.concurrent.atomic.AtomicInteger
import kotlin.coroutines.resume

class PermissionsManagerImpl(
    private val currentActivityProvider: CurrentActivityProvider
) : PermissionsManager {

    private val keyIncrement = AtomicInteger(0)

    private val currentActivity
        get() = currentActivityProvider.currentActivity

    override suspend fun requestPermission(permission: Permission): PermissionStatus {
        val currentActivity = currentActivity ?: return PermissionStatus.Denied(isRationale = false)

        return suspendCancellableCoroutine { continuation ->
            val launcher = currentActivity.activityResultRegistry.register(
                "permission_${keyIncrement.getAndIncrement()}",
                ActivityResultContracts.RequestPermission()
            ) { result ->
                continuation.resume(
                    if (result) {
                        PermissionStatus.Granted
                    } else {
                        val shouldShowRationale = currentActivity.shouldShowRequestPermissionRationale(permission.androidPermission)
                        PermissionStatus.Denied(shouldShowRationale.not())
                    }
                )
            }

            launcher.launch(permission.androidPermission)

            continuation.invokeOnCancellation {
                launcher.unregister()
            }
        }
    }

    override suspend fun requestPermissions(permissions: List<Permission>): Map<Permission, PermissionStatus> {
        val currentActivity = currentActivity ?: return permissions.associateWith {
            PermissionStatus.Denied(isRationale = false)
        }

        return suspendCancellableCoroutine { continuation ->
            val launcher = currentActivity.activityResultRegistry.register(
                "permission_${keyIncrement.getAndIncrement()}",
                ActivityResultContracts.RequestMultiplePermissions()
            ) { result ->
                continuation.resume(permissions.associateWith { permission ->
                    if (result[permission.androidPermission] == true) {
                        PermissionStatus.Granted
                    } else {
                        val shouldShowRationale =
                            currentActivity.shouldShowRequestPermissionRationale(permission.androidPermission)
                        PermissionStatus.Denied(shouldShowRationale)
                    }
                })
            }

            launcher.launch(permissions.map { it.androidPermission }.toTypedArray())

            continuation.invokeOnCancellation {
                launcher.unregister()
            }
        }
    }

    override fun checkPermission(permission: Permission): PermissionStatus {
        val currentActivity = currentActivity ?: return PermissionStatus.Denied(isRationale = false)

        return if (currentActivity.checkSelfPermission(permission.androidPermission) == PackageManager.PERMISSION_GRANTED) {
            PermissionStatus.Granted
        } else {
            val shouldShowRationale = currentActivity.shouldShowRequestPermissionRationale(permission.androidPermission)
            PermissionStatus.Denied(shouldShowRationale)
        }
    }

    override fun checkPermissions(permissions: List<Permission>): Map<Permission, PermissionStatus> {
        return permissions.associateWith { checkPermission(it) }
    }

    override fun openPermissionSettings() {
        val currentActivity = currentActivity ?: return

        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
            data = Uri.fromParts("package", currentActivity.packageName, null)
        }

        currentActivity.startActivity(intent)
    }
}

================
File: app/src/main/java/dev/paypass/system/permission/PermissionStatus.kt
================
package dev.paypass.system.permission

sealed interface PermissionStatus {

    data object Granted : PermissionStatus

    data class Denied(val isRationale: Boolean) : PermissionStatus

    val isDeniedRationale: Boolean
        get() = this is Denied && isRationale

    val isGranted: Boolean
        get() = this is Granted
}

================
File: app/src/main/java/dev/paypass/system/sms/IncomingSms.kt
================
package dev.paypass.system.sms

data class IncomingSms(
    val sender: String,
    val message: String,
    val timestamp: Long
)

================
File: app/src/main/java/dev/paypass/system/sms/IncomingSmsBroadcastReceiver.kt
================
package dev.paypass.system.sms

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.provider.Telephony
import timber.log.Timber

internal class IncomingSmsBroadcastReceiver : BroadcastReceiver() {

    override fun onReceive(context: Context, intent: Intent) {
        if (intent.action != Telephony.Sms.Intents.SMS_RECEIVED_ACTION) return
        val smsReceiver = smsReceiver ?: return

        val smsMessages = Telephony.Sms.Intents.getMessagesFromIntent(intent)

        Timber.d("Received ${smsMessages.size} SMS messages")

        smsMessages.forEach { smsMessage ->
            val incomingSms = IncomingSms(
                sender = smsMessage.displayOriginatingAddress,
                message = smsMessage.displayMessageBody,
                timestamp = smsMessage.timestampMillis
            )

            smsReceiver.onSmsReceived(incomingSms)
        }
    }

    companion object {
        var smsReceiver: IncomingSmsReceiver? = null

        fun register(context: Context, smsReceiver: IncomingSmsReceiver) {
            this.smsReceiver = smsReceiver

            context.registerReceiver(
                IncomingSmsBroadcastReceiver(),
                IntentFilter(Telephony.Sms.Intents.SMS_RECEIVED_ACTION)
            )
        }
    }
}

================
File: app/src/main/java/dev/paypass/system/sms/IncomingSmsReceiver.kt
================
package dev.paypass.system.sms

interface IncomingSmsReceiver {

    fun onSmsReceived(sms: IncomingSms)
}

================
File: app/src/main/java/dev/paypass/system/sms/SmsManager.kt
================
package dev.paypass.system.sms

import kotlinx.coroutines.flow.SharedFlow

interface SmsManager {

    val incomingSms: SharedFlow<IncomingSms>
}

================
File: app/src/main/java/dev/paypass/system/sms/SmsManagerImpl.kt
================
package dev.paypass.system.sms

import com.arkivanov.essenty.lifecycle.doOnStart
import dev.paypass.system.activity.CurrentActivityProvider
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import timber.log.Timber

internal class SmsManagerImpl(
    private val currentActivityProvider: CurrentActivityProvider
) : SmsManager {

    private val _incomingSms = MutableSharedFlow<IncomingSms>()
    override val incomingSms = _incomingSms.asSharedFlow()

    init {
        currentActivityProvider.lifecycle.doOnStart {
            Timber.d("Registering incoming SMS receiver")
            val currentActivity = currentActivityProvider.currentActivity!!
            IncomingSmsBroadcastReceiver.register(currentActivity.applicationContext, incomingSmsReceiver)
        }
    }

    private val incomingSmsReceiver = object : IncomingSmsReceiver {
        override fun onSmsReceived(sms: IncomingSms) {
            Timber.d("onSmsReceived: $sms")
            GlobalScope.launch { _incomingSms.emit(sms) }
        }
    }
}

================
File: app/src/main/java/dev/paypass/system/storage/impl/DataStoreSerializer.kt
================
package dev.paypass.system.storage.impl

typealias DataStoreSerializer<T> = androidx.datastore.core.Serializer<T>

================
File: app/src/main/java/dev/paypass/system/storage/impl/DataStoreStorage.kt
================
package dev.paypass.system.storage.impl

import android.content.Context
import androidx.datastore.dataStore
import dev.paypass.system.storage.Storage
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.mapNotNull

internal class DataStoreStorage<T>(
    private val context: Context,
    dataStoreSerializer: DataStoreSerializer<T?>,
    key: String
): Storage<T> {

    private val Context.dataStore by dataStore(key, dataStoreSerializer)

    override fun changesFlow(): Flow<T> {
        return context.dataStore.data.mapNotNull { it }
    }

    override suspend fun getOrNull(): T? {
        return context.dataStore.data.firstOrNull()
    }

    override suspend fun clear() {
        context.dataStore.updateData { null }
    }

    override suspend fun update(value: T) {
        context.dataStore.updateData { value }
    }
}

================
File: app/src/main/java/dev/paypass/system/storage/impl/DataStoreStorageFactory.kt
================
package dev.paypass.system.storage.impl

import android.content.Context
import dev.paypass.system.storage.Storage
import dev.paypass.system.storage.StorageFactory
import kotlinx.serialization.StringFormat
import kotlinx.serialization.json.Json

internal class DataStoreStorageFactory(
    private val context: Context,
) : StorageFactory{

    override fun <T : Any> createStorage(
        key: String,
        clazz: Class<T>,
        defaultValue: T?
    ): Storage<T> {
        val dataStoreSerializer = JsonDataStoreSerializer(
            typeOfT = clazz,
            stringFormat = stringFormat,
            defaultValue = defaultValue
        )

        return DataStoreStorage(context, dataStoreSerializer, key)
    }

    companion object {
        private val stringFormat: StringFormat = Json {
            ignoreUnknownKeys = true
        }
    }
}

================
File: app/src/main/java/dev/paypass/system/storage/impl/InMemoryStorage.kt
================
package dev.paypass.system.storage.impl

import dev.paypass.system.storage.Storage
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.filterNotNull

internal class InMemoryStorage<T>: Storage<T> {

    private val value = MutableStateFlow<T?>(null)

    override fun changesFlow(): Flow<T> {
        return value.asStateFlow().filterNotNull()
    }

    override suspend fun getOrNull(): T? {
        return value.value
    }

    override suspend fun clear() {
        value.value = null
    }

    override suspend fun update(value: T) {
        this.value.value = value
    }
}

================
File: app/src/main/java/dev/paypass/system/storage/impl/InMemoryStorageFactory.kt
================
package dev.paypass.system.storage.impl

import dev.paypass.system.storage.Storage
import dev.paypass.system.storage.StorageFactory

internal class InMemoryStorageFactory: StorageFactory {

    override fun <T : Any> createStorage(
        key: String,
        clazz: Class<T>,
        defaultValue: T?
    ): Storage<T> {
        return InMemoryStorage()
    }
}

================
File: app/src/main/java/dev/paypass/system/storage/impl/JsonDataStoreSerializer.kt
================
package dev.paypass.system.storage.impl

import androidx.datastore.core.CorruptionException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.StringFormat
import kotlinx.serialization.serializer
import java.io.InputStream
import java.io.OutputStream
import java.lang.reflect.Type

internal class JsonDataStoreSerializer<T: Any>(
    typeOfT: Type,
    private val stringFormat: StringFormat,
    override val defaultValue: T?
): DataStoreSerializer<T?> {

    @Suppress("UNCHECKED_CAST")
    private val kSerializer = serializer(typeOfT) as kotlinx.serialization.KSerializer<T>

    override suspend fun readFrom(input: InputStream): T {
        try {
            val bytes = input.readBytes()
            val string = bytes.decodeToString()

            return stringFormat.decodeFromString(kSerializer, string)
        } catch (e: Exception) {
            throw CorruptionException("Cannot read data", e)
        }
    }

    override suspend fun writeTo(t: T?, output: OutputStream) {
        val string = stringFormat.encodeToString(kSerializer, t ?: return)
        val bytes = string.encodeToByteArray()

        withContext(Dispatchers.IO) {
            output.write(bytes)
        }
    }

}

================
File: app/src/main/java/dev/paypass/system/storage/impl/StorageFactoryProviderImpl.kt
================
package dev.paypass.system.storage.impl

import android.content.Context
import dev.paypass.system.storage.StorageFactory
import dev.paypass.system.storage.StorageFactoryProvider
import dev.paypass.system.storage.StorageType

internal class StorageFactoryProviderImpl(
    private val context: Context
) : StorageFactoryProvider {

    override fun getStorageFactory(storageType: StorageType): StorageFactory {
        return when (storageType) {
            StorageType.PERSISTENT -> DataStoreStorageFactory(context)
            StorageType.IN_MEMORY -> InMemoryStorageFactory()
        }
    }
}

================
File: app/src/main/java/dev/paypass/system/storage/Storage.kt
================
package dev.paypass.system.storage

import kotlinx.coroutines.flow.Flow

interface Storage<T> {

    fun changesFlow(): Flow<T>

    suspend fun getOrNull(): T?

    suspend fun update(value: T)

    suspend fun clear()
}

================
File: app/src/main/java/dev/paypass/system/storage/StorageExtensions.kt
================
package dev.paypass.system.storage

suspend fun <T> Storage<T>.update(block: (T?) -> T) {
    val valueOrNull = getOrNull()
    val updatedValue = block(valueOrNull)

    update(updatedValue)
}

================
File: app/src/main/java/dev/paypass/system/storage/StorageFactory.kt
================
package dev.paypass.system.storage

interface StorageFactory {

    fun <T : Any> createStorage(
        key: String,
        clazz: Class<T>,
        defaultValue: T? = null
    ): Storage<T>
}

================
File: app/src/main/java/dev/paypass/system/storage/StorageFactoryProvider.kt
================
package dev.paypass.system.storage

interface StorageFactoryProvider {

    fun getStorageFactory(storageType: StorageType): StorageFactory
}

================
File: app/src/main/java/dev/paypass/system/storage/StorageType.kt
================
package dev.paypass.system.storage

enum class StorageType {
    PERSISTENT,
    IN_MEMORY
}

================
File: app/src/main/java/dev/paypass/system/thirdparty/QrScanLauncher.kt
================
package dev.paypass.system.thirdparty

interface QrScanLauncher {

    suspend fun launchQrScan(): QrScanResult
}

================
File: app/src/main/java/dev/paypass/system/thirdparty/QrScanLauncherImpl.kt
================
package dev.paypass.system.thirdparty

import dev.paypass.system.activity.CurrentActivityProvider
import io.github.g00fy2.quickie.QRResult
import io.github.g00fy2.quickie.ScanQRCode
import kotlinx.coroutines.suspendCancellableCoroutine
import java.util.concurrent.atomic.AtomicInteger
import kotlin.coroutines.resume

internal class QrScanLauncherImpl(
    private val currentActivityProvider: CurrentActivityProvider
) : QrScanLauncher {

    private val keyIncrement = AtomicInteger(0)

    private val currentActivity
        get() = currentActivityProvider.currentActivity ?: error("No current activity")

    private val nextRequestKey: String
        get() = "qr_scan_${keyIncrement.getAndIncrement()}"

    override suspend fun launchQrScan(): QrScanResult {
        return suspendCancellableCoroutine { continuation ->
            val launcher = currentActivity.activityResultRegistry.register(
                nextRequestKey,
                ScanQRCode()
            ) { result ->
                continuation.resume(result.mapToQrScanResult())
            }

            launcher.launch(null)

            continuation.invokeOnCancellation {
                launcher.unregister()
            }
        }
    }

    private fun QRResult.mapToQrScanResult(): QrScanResult {
        return when (this) {
            is QRResult.QRSuccess -> QrScanResult.Success(
                rawBytes = content.rawBytes ?: byteArrayOf(),
                rawValue = content.rawValue ?: ""
            )
            QRResult.QRUserCanceled -> QrScanResult.UserCanceled
            QRResult.QRMissingPermission -> QrScanResult.MissingPermission
            is QRResult.QRError -> QrScanResult.Error(this.exception)
        }
    }
}

================
File: app/src/main/java/dev/paypass/system/thirdparty/QrScanResult.kt
================
package dev.paypass.system.thirdparty

import java.lang.Exception

sealed interface QrScanResult {

    data class Success(
        val rawBytes: ByteArray,
        val rawValue: String
    ) : QrScanResult {
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false

            other as Success

            if (!rawBytes.contentEquals(other.rawBytes)) return false
            if (rawValue != other.rawValue) return false

            return true
        }

        override fun hashCode(): Int {
            var result = rawBytes.contentHashCode()
            result = 31 * result + rawValue.hashCode()
            return result
        }
    }

    data object UserCanceled : QrScanResult

    data object MissingPermission : QrScanResult

    data class Error(
        val exception: Exception
    ) : QrScanResult {

        val message: String
            get() = exception.message ?: ""
    }
}

================
File: app/src/main/java/dev/paypass/system/util/DefaultActivityLifecycleCallbacks.kt
================
package dev.paypass.system.util

import android.app.Activity
import android.app.Application
import android.os.Bundle

interface DefaultActivityLifecycleCallbacks : Application.ActivityLifecycleCallbacks {

    override fun onActivityPreCreated(activity: Activity, savedInstanceState: Bundle?) {

    }

    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {

    }

    override fun onActivityPostCreated(activity: Activity, savedInstanceState: Bundle?) {

    }

    override fun onActivityPreStarted(activity: Activity) {

    }

    override fun onActivityStarted(activity: Activity) {

    }

    override fun onActivityPostStarted(activity: Activity) {

    }

    override fun onActivityPreResumed(activity: Activity) {

    }

    override fun onActivityResumed(activity: Activity) {

    }

    override fun onActivityPostResumed(activity: Activity) {

    }

    override fun onActivityPrePaused(activity: Activity) {

    }

    override fun onActivityPaused(activity: Activity) {

    }

    override fun onActivityPostPaused(activity: Activity) {

    }

    override fun onActivityPreStopped(activity: Activity) {

    }

    override fun onActivityStopped(activity: Activity) {

    }

    override fun onActivityPostStopped(activity: Activity) {

    }

    override fun onActivityPreSaveInstanceState(activity: Activity, outState: Bundle) {

    }

    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {

    }

    override fun onActivityPostSaveInstanceState(activity: Activity, outState: Bundle) {

    }

    override fun onActivityPreDestroyed(activity: Activity) {

    }

    override fun onActivityDestroyed(activity: Activity) {

    }

    override fun onActivityPostDestroyed(activity: Activity) {

    }
}

================
File: app/src/main/java/dev/paypass/ui/component/app_conditions/widget/NotificationListenersDialog.kt
================
package dev.paypass.ui.component.app_conditions.widget

import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.text.style.TextAlign

@Composable
internal fun AppConditionsNotificationListenersDialog(
    onDismissRequest: () -> Unit,
    onOpenSettingsClicked: () -> Unit
) {
    AlertDialog(
        title = {
            Text(
                text = "Необходимо разрешение",
                textAlign = TextAlign.Center
            )
        },
        text = {
            Text(
                text = "Для корректной работы приложения необходимо разрешить доступ к уведомлениям. Пожалуйста, разрешите доступ в настройках уведомлений.",
                textAlign = TextAlign.Center
            )
        },
        onDismissRequest = onDismissRequest,
        confirmButton = {
            TextButton(
                onClick = onOpenSettingsClicked
            ) {
                Text("НАСТРОЙКИ")
            }
        },
    )
}

================
File: app/src/main/java/dev/paypass/ui/component/app_conditions/widget/PermissionDialog.kt
================
package dev.paypass.ui.component.app_conditions.widget

import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.text.style.TextAlign

@Composable
internal fun AppConditionsPermissionDialog(
    onDismissRequest: () -> Unit,
    onOpenSettingsClicked: () -> Unit
) {
    AlertDialog(
        title = {
            Text(
                text = "Необходимо разрешение",
                textAlign = TextAlign.Center
            )
        },
        text = {
            Text(
                text = "Для корректной работы приложения необходимо разрешить доступ к СМС. Пожалуйста, включите все необходимые разрешения в настройках приложения.",
                textAlign = TextAlign.Center
            )
        },
        onDismissRequest = onDismissRequest,
        confirmButton = {
            TextButton(
                onClick = onOpenSettingsClicked
            ) {
                Text("НАСТРОЙКИ")
            }
        },
    )
}

================
File: app/src/main/java/dev/paypass/ui/component/app_conditions/AppConditionsScreenComponent.kt
================
package dev.paypass.ui.component.app_conditions

import dev.paypass.domain.condition.AppCondition
import kotlinx.coroutines.flow.StateFlow

interface AppConditionsScreenComponent {

    val state: StateFlow<AppConditionsScreenState>

    fun onConditionItemClicked(appCondition: AppCondition)

    fun onPermissionDialogDismissRequest()

    fun onPermissionDialogOpenSettingsClicked()

    fun onEnableNotificationListenerDialogDismissRequest()

    fun onEnableNotificationListenerDialogOpenSettingsClicked()

    interface Callbacks {
        fun onAllConditionsSatisfied()
    }
}

================
File: app/src/main/java/dev/paypass/ui/component/app_conditions/AppConditionsScreenComponentImpl.kt
================
package dev.paypass.ui.component.app_conditions

import android.os.Build
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.essenty.lifecycle.doOnResume
import dev.paypass.domain.condition.AppCondition
import dev.paypass.domain.condition.GetAllAppConditionsUseCase
import dev.paypass.system.notification.SystemNotificationsManager
import dev.paypass.system.permission.Permission
import dev.paypass.system.permission.PermissionsManager
import dev.paypass.ui.util.componentScope
import kotlinx.collections.immutable.toImmutableMap
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

class AppConditionsScreenComponentImpl(
    context: ComponentContext,
    private val callbacks: AppConditionsScreenComponent.Callbacks,
    private val getAllAppConditionsUseCase: GetAllAppConditionsUseCase,
    private val permissionsManager: PermissionsManager,
    private val systemNotificationsManager: SystemNotificationsManager,
) : AppConditionsScreenComponent, ComponentContext by context {

    private val _state = MutableStateFlow(AppConditionsScreenState())
    override val state = _state.asStateFlow()

    init {
        lifecycle.doOnResume { checkAllConditions() }
    }

    override fun onConditionItemClicked(appCondition: AppCondition) {
        val isConditionSatisfied = state.value.appConditions[appCondition] ?: return
        if (isConditionSatisfied) return

        when (appCondition) {
            is AppCondition.Permission -> {
                componentScope.launch {
                    val permission = when (appCondition) {
                        AppCondition.Permission.READ_SMS -> Permission.READ_SMS
                        AppCondition.Permission.RECEIVE_SMS -> Permission.RECEIVE_SMS
                        AppCondition.Permission.POST_NOTIFICATIONS -> if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                            Permission.POST_NOTIFICATIONS
                        } else {
                            return@launch
                        }
                    }

                    val permissionStatus = permissionsManager.requestPermission(permission)

                    if (permissionStatus.isDeniedRationale) {
                        setShowingPermissionDialog(true)
                    }
                }
            }

            AppCondition.EnableNotificationListener -> {
                setShowingEnableNotificationListenerDialog(true)
            }
        }
    }

    override fun onPermissionDialogDismissRequest() {
        setShowingPermissionDialog(false)
    }

    override fun onPermissionDialogOpenSettingsClicked() {
        setShowingPermissionDialog(false)

        permissionsManager.openPermissionSettings()
    }

    override fun onEnableNotificationListenerDialogDismissRequest() {
        setShowingEnableNotificationListenerDialog(false)
    }

    override fun onEnableNotificationListenerDialogOpenSettingsClicked() {
        setShowingEnableNotificationListenerDialog(false)

        systemNotificationsManager.openNotificationListenerSettings()
    }

    private fun setShowingPermissionDialog(show: Boolean) {
        _state.update { state -> state.copy(isShowingPermissionDialog = show) }
    }

    private fun setShowingEnableNotificationListenerDialog(show: Boolean) {
        _state.update { state -> state.copy(isShowingEnableNotificationListenerDialog = show) }
    }

    private fun checkAllConditions() {
        val appConditions = getAllAppConditionsUseCase().toImmutableMap()
        val isAllConditionsSatisfied = appConditions.values.all { it }

        _state.update { state -> state.copy(appConditions = appConditions) }

        if (isAllConditionsSatisfied) {
            callbacks.onAllConditionsSatisfied()
        }
    }
}

================
File: app/src/main/java/dev/paypass/ui/component/app_conditions/AppConditionsScreenState.kt
================
package dev.paypass.ui.component.app_conditions

import dev.paypass.domain.condition.AppCondition
import kotlinx.collections.immutable.ImmutableMap
import kotlinx.collections.immutable.persistentMapOf

data class AppConditionsScreenState(
    val appConditions: ImmutableMap<AppCondition, Boolean> = persistentMapOf( ),
    val isShowingPermissionDialog: Boolean = false,
    val isShowingEnableNotificationListenerDialog: Boolean = false,
) {

}

================
File: app/src/main/java/dev/paypass/ui/component/app_conditions/AppConditionsScreenUI.kt
================
package dev.paypass.ui.component.app_conditions

import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import dev.paypass.domain.condition.AppCondition
import dev.paypass.ui.component.app_conditions.widget.AppConditionsNotificationListenersDialog
import dev.paypass.ui.component.app_conditions.widget.AppConditionsPermissionDialog
import kotlinx.collections.immutable.ImmutableMap
import kotlinx.collections.immutable.persistentMapOf

@Composable
fun AppConditionsScreenUI(
    modifier: Modifier = Modifier,
    component: AppConditionsScreenComponent,
) {
    val state by component.state.collectAsState()

    if (state.isShowingPermissionDialog) {
        AppConditionsPermissionDialog(
            onDismissRequest = component::onPermissionDialogDismissRequest,
            onOpenSettingsClicked = component::onPermissionDialogOpenSettingsClicked
        )
    }

    if (state.isShowingEnableNotificationListenerDialog) {
        AppConditionsNotificationListenersDialog(
            onDismissRequest = component::onEnableNotificationListenerDialogDismissRequest,
            onOpenSettingsClicked = component::onEnableNotificationListenerDialogOpenSettingsClicked
        )
    }

    AppConditionsScreenNode(
        modifier = modifier,
        conditions = state.appConditions,
        onConditionItemClicked = component::onConditionItemClicked
    )
}

@Composable
private fun AppConditionsScreenNode(
    modifier: Modifier = Modifier,
    conditions: ImmutableMap<AppCondition, Boolean> = persistentMapOf(),
    onConditionItemClicked: (AppCondition) -> Unit
) {
    Column(
        modifier = modifier
    ) {
        Spacer(modifier = Modifier.fillMaxHeight(0.1f))

        AppConditionsHeader(
            modifier = Modifier.padding(horizontal = 16.dp)
        )

        Spacer(Modifier.fillMaxHeight(0.1f))

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            contentAlignment = Alignment.Center
        ) {
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                conditions.forEach { (condition, isSatisfied) ->
                    ConditionListItem(
                        modifier = Modifier.fillMaxWidth(),
                        appCondition = condition,
                        isSatisfied = isSatisfied,
                        onConditionClicked = { onConditionItemClicked(condition) }
                    )
                }
            }
        }
    }
}

@Composable
private fun ConditionListItem(
    modifier: Modifier = Modifier,
    appCondition: AppCondition,
    isSatisfied: Boolean,
    onConditionClicked: (AppCondition) -> Unit
) {
    val borderColor = when (isSatisfied) {
        true -> Color.Green
        false -> MaterialTheme.colorScheme.error
    }

    Row(
        modifier = modifier
            .clip(MaterialTheme.shapes.medium)
            .clickable(
                onClick = { onConditionClicked(appCondition) }
            )
            .border(
                width = 1.dp,
                color = borderColor,
                shape = MaterialTheme.shapes.medium
            )
            .padding(8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        ConditionStatusIndicator(
            modifier = Modifier.size(24.dp),
            isSatisfied = isSatisfied
        )

        Spacer(Modifier.width(8.dp))

        Column {
            Text(
                text = when (appCondition) {
                    AppCondition.EnableNotificationListener -> "Получение push-уведомлений"
                    AppCondition.Permission.READ_SMS -> "Чтение SMS"
                    AppCondition.Permission.RECEIVE_SMS -> "Получение SMS"
                    AppCondition.Permission.POST_NOTIFICATIONS -> "Отправка уведомлений"
                },
                style = MaterialTheme.typography.bodyMedium
            )
            Text(
                text = when (appCondition) {
                    AppCondition.EnableNotificationListener -> "Для получения push-уведомлений необходимо разрешить доступ к уведомлениям"
                    AppCondition.Permission.READ_SMS -> "Для корректной работы приложения необходимо разрешить доступ к чтению SMS"
                    AppCondition.Permission.RECEIVE_SMS -> "Для корректной работы приложения необходимо разрешить доступ к получению SMS"
                    AppCondition.Permission.POST_NOTIFICATIONS -> "Для корректной работы приложения необходимо разрешить доступ к отправке уведомлений"
                },
                style = MaterialTheme.typography.bodySmall
            )
        }
    }
}

@Composable
private fun ConditionStatusIndicator(
    modifier: Modifier = Modifier,
    isSatisfied: Boolean
) {
    Icon(
        modifier = modifier,
        imageVector = when (isSatisfied) {
            true -> Icons.Default.Check
            false -> Icons.Default.Close
        },
        contentDescription = null
    )
}

@Composable
private fun AppConditionsHeader(
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
    ) {
        Text(
            modifier = Modifier.align(Alignment.CenterHorizontally),
            text = "Разрешения",
            style = MaterialTheme.typography.headlineMedium
        )

        Spacer(Modifier.height(8.dp))

        Text(
            text = "Для корректной работы приложения требуется разрешить доступ к следующим функциям",
            style = MaterialTheme.typography.bodyMedium,
            textAlign = TextAlign.Center
        )
    }
}

@Preview
@Composable
private fun PermissionsScreenNodePreview() {
    Surface {
        AppConditionsScreenNode(
            modifier = Modifier.fillMaxSize(),
            conditions = persistentMapOf(
                AppCondition.Permission.READ_SMS to true,
                AppCondition.EnableNotificationListener to false,
            ),
            onConditionItemClicked = {}
        )
    }
}

================
File: app/src/main/java/dev/paypass/ui/component/main/widget/MainAuthBlock.kt
================
package dev.paypass.ui.component.main.widget

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Button
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp

@Composable
internal fun MainAuthBlock(
    modifier: Modifier = Modifier,
    phoneFieldText: String?,
    phoneFieldValueChanged: (String) -> Unit,
    isSavePhoneButtonEnabled: Boolean,
    onSavePhoneButtonClicked: () -> Unit,
    tokenValue: String?,
    onScanQrCodeClicked: () -> Unit,
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(16.dp),
    ) {
        SettingsPhoneItem(
            modifier = Modifier.fillMaxWidth(),
            fieldText = phoneFieldText,
            fieldValueChanged = phoneFieldValueChanged,
            isSaveButtonEnabled = isSavePhoneButtonEnabled,
            onSaveButtonClicked = onSavePhoneButtonClicked,
        )

        HorizontalDivider()

        SettingsTokenItem(
            modifier = Modifier.fillMaxWidth(),
            fieldText = tokenValue,
            onScanQrCodeClicked = onScanQrCodeClicked,
        )
    }
}

@Composable
private fun SettingsPhoneItem(
    modifier: Modifier = Modifier,
    fieldText: String?,
    fieldValueChanged: (String) -> Unit,
    isSaveButtonEnabled: Boolean,
    onSaveButtonClicked: () -> Unit,
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        OutlinedTextField(
            modifier = Modifier.weight(1F),
            value = fieldText ?: "",
            label = { Text(text = "ID (номер телефона)") },
            onValueChange = fieldValueChanged,
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Phone
            ),
            singleLine = true
        )

        Button(
            enabled = isSaveButtonEnabled,
            onClick = onSaveButtonClicked,
        ) {
            Text(
                text = "ОБНОВИТЬ",
                maxLines = 1
            )
        }
    }
}

@Composable
private fun SettingsTokenItem(
    modifier: Modifier = Modifier,
    fieldText: String?,
    onScanQrCodeClicked: () -> Unit,
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(4.dp),
    ) {
        Text(
            text = "Токен",
            maxLines = 1,
            style = MaterialTheme.typography.labelLarge
        )

        Row(
            horizontalArrangement = Arrangement.spacedBy(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                modifier = Modifier.weight(1F),
                text = fieldText ?: "N/A",
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )

            Button(
                onClick = onScanQrCodeClicked,
            ) {
                Text(
                    text = "QR",
                    maxLines = 1,
                )
            }
        }
    }
}

================
File: app/src/main/java/dev/paypass/ui/component/main/widget/MainCameraPermissionDialog.kt
================
package dev.paypass.ui.component.main.widget

import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.text.style.TextAlign

@Composable
internal fun MainCameraPermissionDialog(
    isDeniedForever: Boolean,
    onDismissRequest: () -> Unit,
    onOpenSettingsClicked: () -> Unit
) {
    AlertDialog(
        title = {
            Text(
                text = "Необходимо разрешение",
                textAlign = TextAlign.Center
            )
        },
        text = {
            Text(
                text = if (isDeniedForever) {
                    "Для корректной работы приложения необходимо разрешить доступ к камере. Пожалуйста, включите все необходимые разрешения в настройках приложения."
                } else {
                    "Для корректной работы приложения необходимо разрешить доступ к камере"
                },
                textAlign = TextAlign.Center
            )
        },
        onDismissRequest = onDismissRequest,
        confirmButton = {
            TextButton(
                onClick = if (isDeniedForever) {
                    onOpenSettingsClicked
                } else {
                    onDismissRequest
                }
            ) {
                Text(
                    text = if (isDeniedForever) {
                        "НАСТРОЙКИ"
                    } else {
                        "ОК"
                    }
                )
            }
        },
    )
}

================
File: app/src/main/java/dev/paypass/ui/component/main/widget/MainDebugInfoBlock.kt
================
package dev.paypass.ui.component.main.widget

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf

@Composable
internal fun MainDebugInfoBlock(
    modifier: Modifier = Modifier,
    urlForPushValue: String?,
    urlForSmsValue: String?,
    urlForHeartBeatValue: String?,
    worksInQueueCount: Int,
    worksInErrorCount: Int,
) {
    Column(
        modifier = modifier,
    ) {
        Text(
            text = "Debug Info",
            maxLines = 1,
            style = MaterialTheme.typography.labelLarge
        )

        MainDebugInfoColumnItem(
            items = persistentListOf(
                ColumnItemState(
                    title = "Push",
                    value = urlForPushValue ?: "N/N",
                    color = if (urlForPushValue != null) MaterialTheme.colorScheme.onSurface else Color.Red
                ),
                ColumnItemState(
                    title = "SMS",
                    value = urlForSmsValue ?: "N/N",
                    color = if (urlForSmsValue != null) MaterialTheme.colorScheme.onSurface else Color.Red
                ),
                ColumnItemState(
                    title = "HeartBeat",
                    value = urlForHeartBeatValue ?: "N/N",
                    color = if (urlForHeartBeatValue != null) MaterialTheme.colorScheme.onSurface else Color.Red
                ),
                ColumnItemState(
                    title = "Задачи в очереди",
                    value = worksInQueueCount.toString(),
                    color = Color.Yellow
                ),
                ColumnItemState(
                    title = "Задачи с ошибкой",
                    value = worksInErrorCount.toString(),
                    color = Color.Red
                )
            )
        )
    }
}

private data class ColumnItemState(
    val title: String,
    val value: String,
    val color: Color
)

@Composable
private fun MainDebugInfoColumnItem(
    modifier: Modifier = Modifier,
    items: ImmutableList<ColumnItemState>
) {
    Box(
        modifier = modifier
            .defaultMinSize(minHeight = 60.dp)
            .padding(vertical = 16.dp),
        contentAlignment = Alignment.Center
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(4.dp),
        ) {
            items.forEach { (title, value, color) ->
                Row(
                    horizontalArrangement = Arrangement.spacedBy(4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        modifier = Modifier.wrapContentHeight(),
                        text = "$title: $value",
                        maxLines = 1,
                        style = MaterialTheme.typography.bodyMedium,
                        color = color
                    )
                }
            }
        }
    }
}

================
File: app/src/main/java/dev/paypass/ui/component/main/MainScreenComponent.kt
================
package dev.paypass.ui.component.main

import kotlinx.coroutines.flow.StateFlow

interface MainScreenComponent {

    val state: StateFlow<MainScreenState>

    fun onIdFieldChanged(idFieldValue: String)

    fun onSavePhoneButtonClicked()

    fun onScanQrClicked()

    fun onCameraPermissionDialogOpenSettingsClicked()

    fun onCameraPermissionDialogDismissRequest()
}

================
File: app/src/main/java/dev/paypass/ui/component/main/MainScreenComponentImpl.kt
================
package dev.paypass.ui.component.main

import com.arkivanov.decompose.ComponentContext
import dev.paypass.domain.auth.use_case.AuthInfoChangesUseCase
import dev.paypass.domain.auth.use_case.AuthSavePhoneNumberUseCase
import dev.paypass.domain.auth.use_case.AuthSaveTokenUseCase
import dev.paypass.domain.config.use_case.RemoteConfigChangesUseCase
import dev.paypass.domain.auth.use_case.AuthScanQrResult
import dev.paypass.domain.auth.use_case.AuthScanQrUseCase
import dev.paypass.domain.work.WorkInfo
import dev.paypass.domain.work.WorkInfoManager
import dev.paypass.system.permission.PermissionsManager
import dev.paypass.ui.util.componentScope
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.merge
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

internal class MainScreenComponentImpl(
    context: ComponentContext,
    private val permissionsManager: PermissionsManager,
    private val authScanQrUseCase: AuthScanQrUseCase,
    private val remoteConfigChangesUseCase: RemoteConfigChangesUseCase,
    private val authInfoChangesUseCase: AuthInfoChangesUseCase,
    private val authSavePhoneNumberUseCase: AuthSavePhoneNumberUseCase,
    private val authSaveTokenUseCase: AuthSaveTokenUseCase,
    private val workInfoManager: WorkInfoManager
) : MainScreenComponent,
    ComponentContext by context {

    private val mutableState = MutableStateFlow(MainScreenState())
    override val state = mutableState.asStateFlow()

    init {
        componentScope.launch { subscribeAuth() }
        componentScope.launch { subscribeRemoteConfig() }
        componentScope.launch { subscribeWorkInfo() }
    }

    override fun onIdFieldChanged(idFieldValue: String) {
        mutableState.update { state ->
            state.copy(
                idFieldText = formatId(idFieldValue)
            )
        }
    }

    override fun onSavePhoneButtonClicked() {
        componentScope.launch {
            val phone = state.value.idFieldText
            authSavePhoneNumberUseCase(phone)
        }
    }

    override fun onScanQrClicked() {
        componentScope.launch {
            when (val result = authScanQrUseCase()) {
                is AuthScanQrResult.Token -> {
                    authSaveTokenUseCase(result.token)
                }

                is AuthScanQrResult.MissingPermission -> {
                    val cameraPermissionDialog = if (result.isPermissionPermanentlyDenied) {
                        MainScreenState.CameraPermissionDialog.DENIED_FOREVER
                    } else {
                        MainScreenState.CameraPermissionDialog.DENIED
                    }

                    mutableState.update { state ->
                        state.copy(cameraPermissionDialog = cameraPermissionDialog)
                    }
                }

                AuthScanQrResult.UserCanceled -> {
                    // no-op
                }
            }
        }
    }

    override fun onCameraPermissionDialogOpenSettingsClicked() {
        mutableState.update { state ->
            state.copy(cameraPermissionDialog = null)
        }
        permissionsManager.openPermissionSettings()
    }

    override fun onCameraPermissionDialogDismissRequest() {
        mutableState.update { state ->
            state.copy(cameraPermissionDialog = null)
        }
    }

    private suspend fun subscribeAuth() {
        val authInfoOrNullChanges = merge(
            authInfoChangesUseCase(),
            flowOf(null)
        )

        coroutineScope {
            launch {
                combine(
                    state.map { it.idFieldText },
                    authInfoOrNullChanges.map { it?.id },
                    ::Pair
                ).collect { (idFieldText, currentId) ->
                    val isChanged = idFieldText != currentId
                    val isIdFieldValid = idFieldText.count { it.isDigit() } == ID_LENGTH

                    mutableState.update { state ->
                        state.copy(
                            isSavePhoneButtonEnabled = isChanged && isIdFieldValid
                        )
                    }
                }
            }

            launch {
                authInfoChangesUseCase().collect { authInfo ->
                    mutableState.update { state ->
                        state.copy(
                            idFieldText = authInfo.id,
                            token = authInfo.token
                        )
                    }
                }
            }
        }
    }

    private suspend fun subscribeRemoteConfig() {
        remoteConfigChangesUseCase().collect { remoteConfig ->
            mutableState.update { state ->
                state.copy(
                    urlForPush = remoteConfig.urlForPush,
                    urlForSms = remoteConfig.urlForSms,
                    urlForHeartBeat = remoteConfig.urlForHeartBeat
                )
            }
        }
    }

    private suspend fun subscribeWorkInfo() {
        workInfoManager.allWorksChanges().collect { works ->
            val worksInQueueCount = works.count {
                it.status == WorkInfo.Status.ENQUEUED || it.status == WorkInfo.Status.RUNNING
            }
            val worksInErrorCount = works.count {
                it.status == WorkInfo.Status.FAILED
            }

            mutableState.update { state ->
                state.copy(
                    worksInQueueCount = worksInQueueCount,
                    worksInErrorCount = worksInErrorCount
                )
            }
        }
    }

    private fun formatId(id: String): String {
        return id
            .replace(Regex("[^0-9]"), "")
            .take(ID_LENGTH)
    }

    companion object {
        private const val ID_LENGTH = 11
    }
}

================
File: app/src/main/java/dev/paypass/ui/component/main/MainScreenNode.kt
================
package dev.paypass.ui.component.main

import android.content.res.Configuration
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Card
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import dev.paypass.ui.component.main.widget.MainAuthBlock
import dev.paypass.ui.component.main.widget.MainDebugInfoBlock
import dev.paypass.ui.theme.PayPassTheme

@Composable
internal fun MainScreenNode(
    modifier: Modifier = Modifier,
    phoneFieldText: String?,
    phoneFieldValueChanged: (String) -> Unit,
    isSavePhoneButtonEnabled: Boolean,
    onSavePhoneButtonClicked: () -> Unit,
    tokenValue: String?,
    onScanQrCodeClicked: () -> Unit,
    urlForPushValue: String?,
    urlForSmsValue: String?,
    urlForHeartBeatValue: String?,
    worksInQueueCount: Int,
    worksInErrorCount: Int,
) {
    Column(
        modifier = modifier
            .padding(
                vertical = 16.dp,
                horizontal = 16.dp,
            ),
        verticalArrangement = Arrangement.spacedBy(16.dp),
    ) {
        Card(
            modifier = Modifier.fillMaxWidth(),
        ) {
            MainAuthBlock(
                modifier = Modifier.padding(16.dp),
                phoneFieldText = phoneFieldText,
                phoneFieldValueChanged = phoneFieldValueChanged,
                isSavePhoneButtonEnabled = isSavePhoneButtonEnabled,
                onSavePhoneButtonClicked = onSavePhoneButtonClicked,
                tokenValue = tokenValue,
                onScanQrCodeClicked = onScanQrCodeClicked,
            )
        }

        HorizontalDivider()

        MainDebugInfoBlock(
            modifier = Modifier.fillMaxWidth(),
            urlForPushValue = urlForPushValue,
            urlForSmsValue = urlForSmsValue,
            urlForHeartBeatValue = urlForHeartBeatValue,
            worksInQueueCount = worksInQueueCount,
            worksInErrorCount = worksInErrorCount,
        )
    }
}

@Preview(uiMode = Configuration.UI_MODE_NIGHT_YES)
@Composable
private fun SettingsScreenNodePreview() = PayPassTheme {
    Surface {
        MainScreenNode(
            modifier = Modifier.fillMaxSize(),
            phoneFieldText = "1234",
            phoneFieldValueChanged = {},
            isSavePhoneButtonEnabled = true,
            onSavePhoneButtonClicked = {},
            tokenValue = null,
            onScanQrCodeClicked = {},
            urlForPushValue = null,
            urlForSmsValue = null,
            urlForHeartBeatValue = null,
            worksInQueueCount = 0,
            worksInErrorCount = 0,
        )
    }
}

================
File: app/src/main/java/dev/paypass/ui/component/main/MainScreenState.kt
================
package dev.paypass.ui.component.main

import androidx.compose.runtime.Immutable

@Immutable
data class MainScreenState(
    val idFieldText: String = "",
    val isSavePhoneButtonEnabled: Boolean = false,
    val token: String? = null,
    val urlForPush: String? = null,
    val urlForSms: String? = null,
    val urlForHeartBeat: String? = null,
    val cameraPermissionDialog: CameraPermissionDialog? = null,
    val worksInQueueCount: Int = 0,
    val worksInErrorCount: Int = 0,
) {

    enum class CameraPermissionDialog {
        DENIED,
        DENIED_FOREVER
    }
}

================
File: app/src/main/java/dev/paypass/ui/component/main/MainScreenUI.kt
================
package dev.paypass.ui.component.main

import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalFocusManager
import dev.paypass.ui.component.main.widget.MainCameraPermissionDialog

@Composable
fun MainScreenUI(
    modifier: Modifier = Modifier,
    component: MainScreenComponent,
) {
    val focusManager = LocalFocusManager.current
    val state by component.state.collectAsState()

    val cameraPermissionDialog = state.cameraPermissionDialog
    if (cameraPermissionDialog != null) {
        MainCameraPermissionDialog(
            isDeniedForever = when (cameraPermissionDialog) {
                MainScreenState.CameraPermissionDialog.DENIED -> false
                MainScreenState.CameraPermissionDialog.DENIED_FOREVER -> true
            },
            onDismissRequest = component::onCameraPermissionDialogDismissRequest,
            onOpenSettingsClicked = component::onCameraPermissionDialogOpenSettingsClicked
        )
    }

    MainScreenNode(
        modifier = modifier
            .pointerInput(Unit) {
                detectTapGestures(
                    onTap = { focusManager.clearFocus() }
                )
            },
        phoneFieldText = state.idFieldText,
        phoneFieldValueChanged = component::onIdFieldChanged,
        isSavePhoneButtonEnabled = state.isSavePhoneButtonEnabled,
        onSavePhoneButtonClicked = {
            focusManager.clearFocus()
            component.onSavePhoneButtonClicked()
        },
        tokenValue = state.token,
        onScanQrCodeClicked = {
            focusManager.clearFocus()
            component.onScanQrClicked()
        },
        urlForPushValue = state.urlForPush,
        urlForSmsValue = state.urlForSms,
        urlForHeartBeatValue = state.urlForHeartBeat,
        worksInQueueCount = state.worksInQueueCount,
        worksInErrorCount = state.worksInErrorCount
    )
}

================
File: app/src/main/java/dev/paypass/ui/component/RootComponent.kt
================
package dev.paypass.ui.component

import com.arkivanov.decompose.router.stack.ChildStack
import dev.paypass.ui.component.app_conditions.AppConditionsScreenComponent
import dev.paypass.ui.component.main.MainScreenComponent
import kotlinx.coroutines.flow.StateFlow

interface RootComponent {

    val childStack: StateFlow<ChildStack<*, Child>>

    sealed interface Child {
        data object Splash : Child

        data class Permissions(val component: AppConditionsScreenComponent) : Child

        data class Settings(val component: MainScreenComponent) : Child
    }
}

================
File: app/src/main/java/dev/paypass/ui/component/RootComponentImpl.kt
================
package dev.paypass.ui.component

import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.router.stack.StackNavigation
import com.arkivanov.decompose.router.stack.childStack
import com.arkivanov.decompose.router.stack.replaceAll
import dev.paypass.domain.condition.GetAllAppConditionsUseCase
import dev.paypass.ui.ComponentFactory
import dev.paypass.ui.component.app_conditions.AppConditionsScreenComponent
import dev.paypass.ui.createComponent
import dev.paypass.ui.util.componentScope
import dev.paypass.ui.util.toStateFlow
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable

class RootComponentImpl(
    context: ComponentContext,
    private val componentFactory: ComponentFactory,
    getAllAppConditionsUseCase: GetAllAppConditionsUseCase,
) : RootComponent,
    ComponentContext by context {

    private val navigation = StackNavigation<Configuration>()

    override val childStack = childStack(
        source = navigation,
        serializer = Configuration.serializer(),
        initialConfiguration = Configuration.Splash,
        childFactory = ::childFactory,
    ).toStateFlow(lifecycle)

    private val permissionsComponentCallbacks = object : AppConditionsScreenComponent.Callbacks {
        override fun onAllConditionsSatisfied() {
            componentScope.launch {
                delay(OPEN_SETTINGS_AFTER_ALL_CONDITIONS_SATISFIED_DELAY_MS)
                navigation.replaceAll(Configuration.Settings)
            }
        }
    }

    init {
        val allAppConditions = getAllAppConditionsUseCase()
        val isAllConditionsSatisfied = allAppConditions.values.all { it }

        if (isAllConditionsSatisfied) {
            navigation.replaceAll(Configuration.Settings)
        } else {
            navigation.replaceAll(Configuration.AppConditions)
        }
    }

    private fun childFactory(
        configuration: Configuration,
        context: ComponentContext
    ): RootComponent.Child {
        return when (configuration) {
            Configuration.Splash -> RootComponent.Child.Splash

            Configuration.AppConditions -> RootComponent.Child.Permissions(
                component = componentFactory.createComponent(context, permissionsComponentCallbacks)
            )

            Configuration.Settings -> RootComponent.Child.Settings(
                component = componentFactory.createComponent(context)
            )
        }
    }

    @Serializable
    sealed interface Configuration {
        @Serializable
        data object Splash : Configuration

        @Serializable
        data object AppConditions : Configuration

        @Serializable
        data object Settings : Configuration
    }

    companion object {
        private const val OPEN_SETTINGS_AFTER_ALL_CONDITIONS_SATISFIED_DELAY_MS = 1_000L
    }
}

================
File: app/src/main/java/dev/paypass/ui/component/RootUI.kt
================
package dev.paypass.ui.component

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import com.arkivanov.decompose.extensions.compose.stack.Children
import dev.paypass.ui.component.app_conditions.AppConditionsScreenUI
import dev.paypass.ui.component.main.MainScreenUI

@Composable
fun RootUI(
    component: RootComponent,
    modifier: Modifier = Modifier,
) {
    val childStack by component.childStack.collectAsState()

    Scaffold(
        modifier = modifier
    ) { innerPadding ->
        Surface(
            modifier = Modifier.padding(innerPadding)
        ) {
            Children(
                modifier = Modifier.fillMaxSize(),
                stack = childStack,
            ) { childCreated ->
                when (val child = childCreated.instance) {
                    is RootComponent.Child.Splash -> SplashUI()

                    is RootComponent.Child.Permissions -> AppConditionsScreenUI(
                        modifier = Modifier.fillMaxSize(),
                        component = child.component,
                    )

                    is RootComponent.Child.Settings -> MainScreenUI(
                        modifier = Modifier.fillMaxSize(),
                        component = child.component,
                    )
                }
            }
        }
    }
}

@Composable
private fun SplashUI(
    modifier: Modifier = Modifier
) {
    // TODO: Implement SplashUI
}

================
File: app/src/main/java/dev/paypass/ui/core/text/MaskOffsetMapping.kt
================
package dev.paypass.ui.core.text

import androidx.compose.ui.text.input.OffsetMapping

internal class MaskOffsetMapping(
    private val mask: String,
    private val originalTextLength: Int,
    private val prefixLength: Int = 0,
    private val isPlaceholder: Char.() -> Boolean
) : OffsetMapping {
    override fun originalToTransformed(offset: Int): Int {
        // original says "123456" with mask "xxx-xxx" -> transformed says "123-456"

        var transformedOffset = offset + prefixLength
        var placeholderCount = 0

        for (maskChar in mask) {
            if (placeholderCount == offset) {
                break
            } else if (maskChar.isPlaceholder()) {
                placeholderCount++
            } else {
                transformedOffset++
            }
        }

        val result = transformedOffset.coerceAtMost(prefixLength + mask.length)

        return result
    }

    override fun transformedToOriginal(offset: Int): Int {
        // transformed says "123-456" with mask "xxx-xxx" -> original says "123456". mapping: 7 -> 6

        var originalOffset = (offset - prefixLength).coerceAtLeast(0)
        var placeholderCount = 0

        for (maskChar in mask) {
            if (placeholderCount == offset) {
                break
            } else if (maskChar.isPlaceholder()) {
                placeholderCount++
            } else {
                originalOffset--
            }
        }

        val result =  originalOffset.coerceIn(minimumValue = 0, maximumValue = originalTextLength)

        return result
    }
}

================
File: app/src/main/java/dev/paypass/ui/core/text/TextMaskedVisualTransformation.kt
================
package dev.paypass.ui.core.text

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.input.TransformedText
import androidx.compose.ui.text.input.VisualTransformation

class TextMaskedVisualTransformation(
    private val mask: String,
    private val prefix: String = "",
    private val maskColor: Color = Color.LightGray,
    private val isMaskCharPlaceholder: (Char) -> Boolean = { char -> char == 'x' }
) : VisualTransformation {

    override fun filter(text: AnnotatedString): TransformedText {
        val maxChars = mask.filter(isMaskCharPlaceholder).length

        val preparedText =
            if (text.text.length >= maxChars) text.text.substring(0..<maxChars) else text.text

        val annotatedString = AnnotatedString.Builder().run {
            append(prefix)

            var offset = 0
            for (i in preparedText.indices) {
                mask.drop(i + offset)
                    .takeWhile { char -> isMaskCharPlaceholder(char).not() }
                    .forEach { append(it); offset++ }

                append(preparedText[i])
            }
            pushStyle(SpanStyle(color = maskColor))

            val remaining = mask.drop(preparedText.length + offset)
            append(remaining)

            toAnnotatedString()
        }

        return TransformedText(
            text = annotatedString,
            offsetMapping = MaskOffsetMapping(
                mask = mask,
                originalTextLength = text.length,
                prefixLength = prefix.length,
                isPlaceholder = isMaskCharPlaceholder
            )
        )
    }
}

================
File: app/src/main/java/dev/paypass/ui/theme/Color.kt
================
package dev.paypass.ui.theme

import androidx.compose.ui.graphics.Color

val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

================
File: app/src/main/java/dev/paypass/ui/theme/Theme.kt
================
package dev.paypass.ui.theme

import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext

private val DarkColorScheme = darkColorScheme(
    primary = Purple80,
    secondary = PurpleGrey80,
    tertiary = Pink80
)

private val LightColorScheme = lightColorScheme(
    primary = Purple40,
    secondary = PurpleGrey40,
    tertiary = Pink40

    /* Other default colors to override
    background = Color(0xFFFFFBFE),
    surface = Color(0xFFFFFBFE),
    onPrimary = Color.White,
    onSecondary = Color.White,
    onTertiary = Color.White,
    onBackground = Color(0xFF1C1B1F),
    onSurface = Color(0xFF1C1B1F),
    */
)

@Composable
fun PayPassTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    // Dynamic color is available on Android 12+
    dynamicColor: Boolean = true,
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }

        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = content
    )
}

================
File: app/src/main/java/dev/paypass/ui/theme/Type.kt
================
package dev.paypass.ui.theme

import androidx.compose.material3.Typography
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp

// Set of Material typography styles to start with
val Typography = Typography(
    bodyLarge = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Normal,
        fontSize = 16.sp,
        lineHeight = 24.sp,
        letterSpacing = 0.5.sp
    )
    /* Other default text styles to override
    titleLarge = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Normal,
        fontSize = 22.sp,
        lineHeight = 28.sp,
        letterSpacing = 0.sp
    ),
    labelSmall = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Medium,
        fontSize = 11.sp,
        lineHeight = 16.sp,
        letterSpacing = 0.5.sp
    )
    */
)

================
File: app/src/main/java/dev/paypass/ui/util/DecomposeExtensions.kt
================
package dev.paypass.ui.util

import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.value.Value
import com.arkivanov.essenty.instancekeeper.InstanceKeeper
import com.arkivanov.essenty.lifecycle.Lifecycle
import com.arkivanov.essenty.lifecycle.doOnDestroy
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlin.coroutines.CoroutineContext

fun <T : Any> Value<T>.toStateFlow(
    lifecycle: Lifecycle
): StateFlow<T> {
    val state = MutableStateFlow(value)

    if (lifecycle.state != Lifecycle.State.DESTROYED) {
        val subscription = subscribe { newValue ->
            state.value = newValue
        }

        lifecycle.doOnDestroy { subscription.cancel() }
    }

    return state
}

private const val INSTANCE_KEY =
    "com.arkivanov.decompose.lifecycle.ComponentContextCoroutineScope.INSTANCE_KEY"

val ComponentContext.componentScope: CoroutineScope
    get() {
        val scope = instanceKeeper.get(INSTANCE_KEY)
        if (scope is CoroutineScope) return scope

        fun destroy() {
            try {
                scope?.onDestroy()
            } catch (e: Exception) {
                throw RuntimeException(e)
            } finally {
                instanceKeeper.remove(INSTANCE_KEY)
            }
        }

        if (lifecycle.state == Lifecycle.State.DESTROYED) {
            destroy()
        } else {
            lifecycle.doOnDestroy {
                destroy()
            }
        }

        return DestroyableCoroutineScope(SupervisorJob() + Dispatchers.Main.immediate).also {
            instanceKeeper.put(INSTANCE_KEY, it)
        }
    }

private class DestroyableCoroutineScope(
    context: CoroutineContext
) : CoroutineScope, InstanceKeeper.Instance {

    override val coroutineContext: CoroutineContext = context

    override fun onDestroy() {
        coroutineContext.cancel()
    }
}

================
File: app/src/main/java/dev/paypass/ui/ComponentFactory.kt
================
package dev.paypass.ui

import com.arkivanov.decompose.ComponentContext
import kotlin.reflect.KClass

interface ComponentFactory {

    fun <T : Any> createComponent(
        context: ComponentContext,
        componentClass: KClass<T>,
        params: Any? = null
    ): T
}

inline fun <reified T : Any> ComponentFactory.createComponent(
    context: ComponentContext,
    params: Any? = null
): T = createComponent(context, T::class, params)

================
File: app/src/main/java/dev/paypass/ui/MainActivity.kt
================
package dev.paypass.ui

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.SystemBarStyle
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.ui.Modifier
import com.arkivanov.decompose.defaultComponentContext
import dev.paypass.ui.component.RootComponent
import dev.paypass.ui.component.RootUI
import dev.paypass.ui.theme.PayPassTheme
import org.koin.android.ext.android.inject

class MainActivity : ComponentActivity() {

    private val componentFactory by inject<ComponentFactory>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        enableEdgeToEdge(
            statusBarStyle = SystemBarStyle.dark(
                scrim = android.graphics.Color.TRANSPARENT,
            )
        )

        setContent {
            PayPassTheme(
                darkTheme = true
            ) {
                RootUI(
                    modifier = Modifier.fillMaxSize(),
                    component = componentFactory.createComponent(
                        context = defaultComponentContext(),
                        componentClass = RootComponent::class
                    )
                )
            }
        }
    }
}

================
File: app/src/main/java/dev/paypass/BootReceiver.kt
================
package dev.paypass

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import dev.paypass.domain.startup.StartupUseCase
import org.koin.core.component.KoinComponent

class BootReceiver : BroadcastReceiver(), KoinComponent {

    override fun onReceive(context: Context, intent: Intent) {
        if (intent.action == Intent.ACTION_BOOT_COMPLETED) {
            val startupUseCase = StartupUseCase.newInstance()

            startupUseCase(context)
        }
    }
}

================
File: app/src/main/java/dev/paypass/KeepLifeService.kt
================
package dev.paypass

import android.annotation.SuppressLint
import android.app.AlarmManager
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.IBinder
import android.os.PowerManager
import android.os.SystemClock
import androidx.core.app.NotificationCompat
import androidx.core.content.getSystemService
import dev.paypass.ui.MainActivity
import timber.log.Timber

internal class KeepLifeService : Service() {

    private var wakeLock: PowerManager.WakeLock? = null

    override fun onBind(p0: Intent?): IBinder? {
        return null
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Timber.d("onStartCommand: $intent with startId: $startId")

        when (val action = intent?.action) {
            ACTION_START -> startService()
            ACTION_STOP -> stopService()
            else -> Timber.e("Unknown action: $action")
        }

        return START_STICKY
    }

    override fun onCreate() {
        super.onCreate()

        val notification = createNotification()
        startForeground(1, notification)
    }

    override fun onTaskRemoved(rootIntent: Intent?) {
        val restartServiceIntent = Intent(applicationContext, this.javaClass).apply {
            `package` = packageName
        }
        val restartServicePendingIntent = PendingIntent.getService(
            applicationContext,
            1,
            restartServiceIntent,
            PendingIntent.FLAG_ONE_SHOT or PendingIntent.FLAG_IMMUTABLE
        )

        val alarmManager = getSystemService<AlarmManager>() ?: error("AlarmManager is null")

        alarmManager.set(
            AlarmManager.ELAPSED_REALTIME,
            SystemClock.elapsedRealtime() + RESCHEDULE_INTERVAL,
            restartServicePendingIntent
        )
    }

    @SuppressLint("WakelockTimeout")
    private fun startService() {
        val powerManager = getSystemService<PowerManager>() ?: error("PowerManager is null")

        wakeLock = powerManager.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, "KeepLifeService:WakeLock")
        wakeLock?.acquire()
    }

    private fun stopService() {
        Timber.d("stopping service")

        try {
            wakeLock?.release()
        } catch (e: Exception) {
            Timber.e(e)
        } finally {
            stopSelf()
        }
    }

    private fun createNotification(): Notification {
        val channelId = "KeepLifeService"
        val channelName = "KeepLifeService"
        val importance = NotificationManager.IMPORTANCE_HIGH
        val channel = NotificationChannel(channelId, channelName, importance)
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.createNotificationChannel(channel)

        val notificationIntent = Intent(this, MainActivity::class.java)

        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            notificationIntent,
            PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, channelId)
            .setContentTitle("KeepLifeService")
            .setContentText("KeepLifeService is running")
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_MIN)
            .setOngoing(true)
            .build()
    }

    companion object {
        private const val ACTION_START = "start"
        private const val ACTION_STOP = "stop"
        private const val RESCHEDULE_INTERVAL = 1000L

        fun startService(context: Context) {
            val intent = Intent(context, KeepLifeService::class.java)
            intent.action = ACTION_START
            context.startService(intent)
        }

        fun stopService(context: Context) {
            val intent = Intent(context, KeepLifeService::class.java)
            intent.action = ACTION_STOP
            context.startService(intent)
        }
    }
}

================
File: app/src/main/java/dev/paypass/StartupInitializer.kt
================
package dev.paypass

import android.content.Context
import androidx.startup.Initializer
import dev.paypass.domain.startup.StartupUseCase

@Suppress("unused") // declared in AndroidManifest.xml and used by the system
class StartupInitializer : Initializer<Context> {

    override fun create(context: Context): Context {
        val startup = StartupUseCase.newInstance()

        startup(context)

        return context
    }

    override fun dependencies(): List<Class<out Initializer<*>>> {
        return emptyList()
    }
}

================
File: app/src/main/AndroidManifest.xml
================
<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-feature
        android:name="android.hardware.telephony"
        android:required="true" />

    <uses-feature
        android:name="android.hardware.camera"
        android:required="true" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_SMS" />
    <uses-permission android:name="android.permission.RECEIVE_SMS" />
    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_REMOTE_MESSAGING" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.PayPass"
        tools:targetApi="31">
        <activity
            android:name=".ui.MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.PayPass">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <receiver
            android:name=".system.sms.IncomingSmsBroadcastReceiver"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.BROADCAST_SMS">

            <intent-filter>
                <action android:name="android.provider.Telephony.SMS_RECEIVED" />
            </intent-filter>
        </receiver>

        <receiver
            android:name=".BootReceiver"
            android:enabled="true"
            android:exported="true"
            >
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>

        <service
            android:name=".KeepLifeService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="remoteMessaging"
            android:stopWithTask="false"/>

        <service
            android:name=".system.notification.AppNotificationListenerService"
            android:exported="true"
            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE">

            <intent-filter>
                <action android:name="android.service.notification.NotificationListenerService" />
            </intent-filter>
        </service>

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            android:exported="false"
            tools:node="merge">

            <meta-data
                android:name="dev.paypass.StartupInitializer"
                android:value="androidx.startup" />
        </provider>
    </application>

</manifest>

================
File: app/src/test/java/dev/paypass/ExampleUnitTest.kt
================
package dev.paypass

import org.junit.Test

import org.junit.Assert.*

/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * See [testing documentation](http://d.android.com/tools/testing).
 */
class ExampleUnitTest {
    @Test
    fun addition_isCorrect() {
        assertEquals(4, 2 + 2)
    }
}

================
File: app/build.gradle.kts
================
plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.compose.compiler)
    alias(libs.plugins.kotlinx.serialization)
    alias(libs.plugins.kotlinx.parcelize)
    alias(libs.plugins.ksp)
    alias(libs.plugins.room)
}

android {
    namespace = "dev.paypass"
    compileSdk = 35

    defaultConfig {
        applicationId = "dev.paypass"
        minSdk = 26
        targetSdk = 35
        versionCode = 1
        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary = true
        }
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = "1.8"
    }
    buildFeatures {
        compose = true
    }
    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.1"
    }
    packaging {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
        }
    }
}

room {
    schemaDirectory("$projectDir/schemas")
}

dependencies {

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.activity.compose)

    implementation(platform(libs.androidx.compose.bom))
    implementation(libs.androidx.ui)
    implementation(libs.androidx.ui.graphics)
    implementation(libs.androidx.ui.tooling.preview)
    implementation(libs.androidx.material3)

    implementation(libs.kotlinx.coroutines.core)
    implementation(libs.kotlinx.collections.immutable)
    implementation(libs.kotlinx.serialization.json)

    implementation(libs.androidx.startup)

    implementation(libs.timber)

    implementation(platform(libs.koin.bom))
    implementation(libs.koin.core)
    implementation(libs.koin.android)

    implementation(libs.sqlite.bundled)
    implementation(libs.room.runtime.android)
    ksp(libs.room.compiler)

    implementation(libs.datastore.preferences)

    implementation(libs.work.runtime)

    implementation(platform(libs.okhttp.bom))
    implementation(libs.okhttp)
    implementation(libs.okhttp.loggingInterceptor)

    implementation(libs.retrofit2)
    implementation(libs.retrofit2.kotlinx.serializationConverter)

    implementation(libs.decompose)
    implementation(libs.decompose.extensions.compose)

    implementation(libs.quickieBundeld)

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    androidTestImplementation(platform(libs.androidx.compose.bom))
    androidTestImplementation(libs.androidx.ui.test.junit4)
    debugImplementation(libs.androidx.ui.tooling)
    debugImplementation(libs.androidx.ui.test.manifest)
}

================
File: build.gradle.kts
================
// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    alias(libs.plugins.android.application) apply false
    alias(libs.plugins.kotlin.android) apply false
    alias(libs.plugins.kotlin.compose.compiler) apply false
    alias(libs.plugins.kotlinx.serialization) apply false
    alias(libs.plugins.kotlinx.parcelize) apply false
    alias(libs.plugins.ksp) apply false
    alias(libs.plugins.room) apply false
}

================
File: settings.gradle.kts
================
pluginManagement {
    repositories {
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.name = "PayPass"
include(":app")
