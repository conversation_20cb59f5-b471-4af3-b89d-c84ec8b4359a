{"formatVersion": 1, "database": {"version": 1, "identityHash": "704b3e24506b75d1ee903125be2e9c93", "entities": [{"tableName": "events_2", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `type` TEXT NOT NULL, `timestamp` INTEGER NOT NULL, `author` TEXT NOT NULL, `message` TEXT NOT NULL, `packageName` TEXT, `isProcessed` INTEGER NOT NULL, `errorReasonType` TEXT, `errorReasonServerCode` INTEGER, `errorReasonMessage` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "author", "columnName": "author", "affinity": "TEXT", "notNull": true}, {"fieldPath": "message", "columnName": "message", "affinity": "TEXT", "notNull": true}, {"fieldPath": "packageName", "columnName": "packageName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isProcessed", "columnName": "isProcessed", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "errorReasonType", "columnName": "errorReasonType", "affinity": "TEXT", "notNull": false}, {"fieldPath": "errorReasonServerCode", "columnName": "errorReasonServerCode", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "errorReasonMessage", "columnName": "errorReasonMessage", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '704b3e24506b75d1ee903125be2e9c93')"]}}