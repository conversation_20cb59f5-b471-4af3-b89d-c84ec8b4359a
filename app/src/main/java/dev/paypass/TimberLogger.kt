package dev.paypass

import dev.paypass.core.analytic.LogEvent
import dev.paypass.core.analytic.Logger
import timber.log.Timber

internal object TimberLogger : Logger {

    init {
        Timber.plant(Timber.DebugTree())
    }

    override fun log(event: LogEvent) {
        when (event) {
            is LogEvent.Error -> Timber.e(event.error, event.message)
            is LogEvent.Action -> Timber.i(event.message)
        }
    }
}