package dev.paypass

import android.annotation.SuppressLint
import android.app.AlarmManager
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.IBinder
import android.os.PowerManager
import android.os.SystemClock
import androidx.core.app.NotificationCompat
import androidx.core.content.getSystemService
import dev.paypass.ui.MainActivity
import timber.log.Timber

internal class KeepLifeService : Service() {

    private var wakeLock: PowerManager.WakeLock? = null

    override fun onBind(p0: Intent?): IBinder? {
        return null
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Timber.d("onStartCommand: $intent with startId: $startId")

        when (val action = intent?.action) {
            ACTION_START -> startService()
            ACTION_STOP -> stopService()
            else -> Timber.e("Unknown action: $action")
        }

        return START_STICKY
    }

    override fun onCreate() {
        super.onCreate()

        val notification = createNotification()
        startForeground(1, notification)
    }

    override fun onTaskRemoved(rootIntent: Intent?) {
        val restartServiceIntent = Intent(applicationContext, this.javaClass).apply {
            `package` = packageName
        }
        val restartServicePendingIntent = PendingIntent.getService(
            applicationContext,
            1,
            restartServiceIntent,
            PendingIntent.FLAG_ONE_SHOT or PendingIntent.FLAG_IMMUTABLE
        )

        val alarmManager = getSystemService<AlarmManager>() ?: error("AlarmManager is null")

        alarmManager.set(
            AlarmManager.ELAPSED_REALTIME,
            SystemClock.elapsedRealtime() + RESCHEDULE_INTERVAL,
            restartServicePendingIntent
        )
    }

    @SuppressLint("WakelockTimeout")
    private fun startService() {
        val powerManager = getSystemService<PowerManager>() ?: error("PowerManager is null")

        wakeLock = powerManager.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, "KeepLifeService:WakeLock")
        wakeLock?.acquire()
    }

    private fun stopService() {
        Timber.d("stopping service")

        try {
            wakeLock?.release()
        } catch (e: Exception) {
            Timber.e(e)
        } finally {
            stopSelf()
        }
    }

    private fun createNotification(): Notification {
        val channelId = "KeepLifeService"
        val channelName = "KeepLifeService"
        val importance = NotificationManager.IMPORTANCE_HIGH
        val channel = NotificationChannel(channelId, channelName, importance)
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.createNotificationChannel(channel)

        val notificationIntent = Intent(this, MainActivity::class.java)

        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            notificationIntent,
            PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, channelId)
            .setContentTitle("PayPass")
            .setContentText("Работает в фоне")
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_MIN)
            .setOngoing(true)
            .build()
    }

    companion object {
        private const val ACTION_START = "start"
        private const val ACTION_STOP = "stop"
        private const val RESCHEDULE_INTERVAL = 1000L

        fun startService(context: Context) {
            val intent = Intent(context, KeepLifeService::class.java)
            intent.action = ACTION_START
            context.startForegroundService(intent)
        }

        fun stopService(context: Context) {
            val intent = Intent(context, KeepLifeService::class.java)
            intent.action = ACTION_STOP
            context.startService(intent)
        }
    }
}