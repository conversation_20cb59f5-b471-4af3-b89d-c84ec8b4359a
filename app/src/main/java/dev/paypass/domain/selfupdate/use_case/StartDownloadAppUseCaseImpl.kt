package dev.paypass.domain.selfupdate.use_case

import dev.paypass.domain.selfupdate.DownloadAppScheduler
import dev.paypass.domain.selfupdate.SelfUpdateRepository

internal class StartDownloadAppUseCaseImpl(
    private val selfUpdateRepository: SelfUpdateRepository,
    private val downloadAppScheduler: DownloadAppScheduler,
) : StartDownloadAppUseCase {

    override fun invoke(apkUrl: String, version: Long) {
        downloadAppScheduler.schedule(
            apkUrl = apkUrl,
            version = version,
            outputFilePath = selfUpdateRepository.preparePathFoSave(apkUrl, version)
        )
    }
}