package dev.paypass.domain.selfupdate.use_case

import dev.paypass.data.selfupdate.work.DownloadAppStatus
import dev.paypass.domain.selfupdate.DownloadAppScheduler
import dev.paypass.domain.selfupdate.SelfUpdateRepository
import dev.paypass.domain.selfupdate.SelfUpdateStatus
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine

internal class SelfUpdateStatusChangesUseCaseImpl(
    private val selfUpdateRepository: SelfUpdateRepository,
    private val downloadAppScheduler: DownloadAppScheduler,
) : SelfUpdateStatusChangesUseCase {

    override fun invoke(): Flow<SelfUpdateStatus> {
        return combine(
            selfUpdateRepository.getUpdateStatus(),
            downloadAppScheduler.getDownloadStatus()
        ) { updateStatus, downloadStatus ->
            when (updateStatus) {
                SelfUpdateRepository.UpdateStatus.LastVersionInstalled -> {
                    SelfUpdateStatus.LastVersionInstalled
                }

                is SelfUpdateRepository.UpdateStatus.NewVersionAvailable -> {
                    when (downloadStatus) {
                        is DownloadAppStatus.None -> {
                            SelfUpdateStatus.NewVersionAvailable(
                                version = updateStatus.version,
                                apkUrl = updateStatus.apkUrl,
                            )
                        }

                        is DownloadAppStatus.InProgress -> {
                            SelfUpdateStatus.Downloading(downloadStatus.progress / 100f)
                        }

                        is DownloadAppStatus.Completed -> {
                            SelfUpdateStatus.ReadyForInstall(
                                version = downloadStatus.version,
                                apkFilePath = downloadStatus.outputFilePath,
                            )
                        }
                    }
                }
            }
        }
    }
}