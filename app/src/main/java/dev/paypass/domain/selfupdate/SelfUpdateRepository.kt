package dev.paypass.domain.selfupdate

import kotlinx.coroutines.flow.StateFlow

interface SelfUpdateRepository {

    suspend fun checkForUpdates()

    fun getUpdateStatus(): StateFlow<UpdateStatus>

    fun preparePathFoSave(apkUrl: String, version: Long): String

    sealed interface UpdateStatus {
        data object LastVersionInstalled : UpdateStatus

        data class NewVersionAvailable(
            val version: Long,
            val apkUrl: String,
        ) : UpdateStatus
    }
}