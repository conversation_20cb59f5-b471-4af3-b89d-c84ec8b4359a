package dev.paypass.domain.selfupdate.use_case

import android.content.Context
import android.content.Intent
import androidx.core.content.FileProvider
import java.io.File

internal class RequestInstallAppUseCaseImpl(
    private val context: Context,
) : RequestInstallAppUseCase {

    override fun invoke(apkFilePath: String) {
        val apkFileUri = FileProvider.getUriForFile(
            context,
            "${context.packageName}.fileprovider",
            File(apkFilePath)
        )

        val intent = Intent(Intent.ACTION_VIEW).apply {
            setDataAndType(apkFileUri, "application/vnd.android.package-archive")
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }

        context.startActivity(intent)
    }
}