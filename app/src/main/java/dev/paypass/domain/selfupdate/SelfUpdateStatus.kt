package dev.paypass.domain.selfupdate

sealed interface SelfUpdateStatus {

    data object LastVersionInstalled : SelfUpdateStatus

    data class NewVersionAvailable(
        val version: Long,
        val apkUrl: String,
    ) : SelfUpdateStatus

    data class Downloading(val progress: Float) : SelfUpdateStatus

    data class ReadyForInstall(
        val version: Long,
        val apkFilePath: String
    ) : SelfUpdateStatus
}