package dev.paypass.domain

import kotlinx.serialization.Serializable

@Serializable
sealed interface Event {

    val id: Long
    val message: String
    val timestamp: Long
    val status: Status

    @Serializable
    data class Sms(
        override val id: Long,
        override val message: String,
        override val timestamp: Long,
        override val status: Status,
        val phoneNumber: String,
    ) : Event

    @Serializable
    data class Notification(
        override val id: Long,
        override val message: String,
        override val timestamp: Long,
        override val status: Status,
        val title: String,
        val packageName: String,
    ) : Event

    @Serializable
    sealed interface Status {

        @Serializable
        data object New : Status

        @Serializable
        data object Processed : Status

        @Serializable
        sealed interface Error : Status {

            @Serializable
            data object NoInternet : Error

            @Serializable
            data class ServerError(
                val code: Int,
                val message: String
            ) : Error

            @Serializable
            data class Unknown(
                val message: String
            ) : Error
        }
    }
}