package dev.paypass.domain.startup

import android.content.Context
import com.google.firebase.FirebaseApp
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.crashlytics.FirebaseCrashlytics
import dev.paypass.FirebaseAnalyticsLogger
import dev.paypass.core.analytic.AppAction
import dev.paypass.FirebaseCrashlyticsLogger
import dev.paypass.TimberLogger
import dev.paypass.core.analytic.Logger
import dev.paypass.core.analytic.LoggerRegistry
import dev.paypass.di.module.appModule
import dev.paypass.di.module.componentModule
import dev.paypass.di.module.dataModule
import dev.paypass.di.module.domainModule
import dev.paypass.di.module.networkModule
import dev.paypass.di.module.systemModule
import dev.paypass.domain.config.remote.use_case.StartPeriodicLoadConfigUseCase
import dev.paypass.domain.debug.RemoteLoggingByConfigUseCase
import dev.paypass.domain.event.use_case.SaveSmsEventsUseCase
import dev.paypass.domain.event.use_case.SaveSystemNotificationEventsUseCase
import dev.paypass.domain.event.use_case.SendNotProcessedEventsUseCase
import dev.paypass.domain.heartbeat.StartPeriodicPushHeartbeatUseCase
import dev.paypass.domain.heartbeat.StartPeriodicSendHeartbeatUseCase
import dev.paypass.domain.notification.StartNotificationManagerUseCase
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import org.koin.android.ext.koin.androidContext
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import org.koin.core.context.GlobalContext
import kotlin.getValue

internal class StartupUseCaseImpl : StartupUseCase, KoinComponent {

    private val scope = CoroutineScope(Dispatchers.Default + SupervisorJob())

    private val firebaseCrashlytics: FirebaseCrashlytics by inject()
    private val firebaseAnalytics: FirebaseAnalytics by inject()

    private val loggerRegistry: LoggerRegistry by inject()
    private val logger: Logger by inject()

    private val remoteLoggingByConfigUseCase: RemoteLoggingByConfigUseCase by inject()
    private val startNotificationManagerUseCase: StartNotificationManagerUseCase by inject()
    private val startPeriodicLoadConfigUseCase: StartPeriodicLoadConfigUseCase by inject()
    private val saveSmsEventsUseCase: SaveSmsEventsUseCase by inject()
    private val saveSystemNotificationEventsUseCase: SaveSystemNotificationEventsUseCase by inject()
    private val sendNotProcessedEventsUseCase: SendNotProcessedEventsUseCase by inject()
    private val startPeriodicSendHeartbeatUseCase: StartPeriodicSendHeartbeatUseCase by inject()
    private val startPeriodicPushHeartbeatUseCase: StartPeriodicPushHeartbeatUseCase by inject()

    override fun invoke(context: Context) {
        FirebaseApp.initializeApp(context)

        if (startKoinIfNotStarted(context)) {
            loggerRegistry.register(TimberLogger)
            loggerRegistry.register(FirebaseCrashlyticsLogger(firebaseCrashlytics))
            loggerRegistry.register(FirebaseAnalyticsLogger(firebaseAnalytics))

            scope.launch { remoteLoggingByConfigUseCase() }
            scope.launch { startNotificationManagerUseCase() }
            scope.launch { saveSmsEventsUseCase() }
            scope.launch { saveSystemNotificationEventsUseCase() }
            scope.launch { sendNotProcessedEventsUseCase() }
            scope.launch { startPeriodicLoadConfigUseCase() }
            scope.launch { startPeriodicSendHeartbeatUseCase() }
            scope.launch { startPeriodicPushHeartbeatUseCase() }

            logger.log(AppAction.Start)
        }
    }

    private fun startKoinIfNotStarted(context: Context): Boolean {
        if (GlobalContext.getOrNull() == null) {
            GlobalContext.startKoin {
                androidContext(context)

                modules(
                    appModule,
                    systemModule,
                    networkModule,
                    dataModule,
                    domainModule,
                    componentModule
                )
            }.koin

            return true
        }

        return false
    }
}