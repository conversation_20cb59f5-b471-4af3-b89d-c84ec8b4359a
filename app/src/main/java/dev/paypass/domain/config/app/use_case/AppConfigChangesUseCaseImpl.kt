package dev.paypass.domain.config.app.use_case

import dev.paypass.domain.config.app.AppConfigRepository
import dev.paypass.domain.config.app.AppConfig
import kotlinx.coroutines.flow.Flow

internal class AppConfigChangesUseCaseImpl(
    private val appConfigRepository: AppConfigRepository
) : AppConfigChangesUseCase {

    override fun invoke(): Flow<AppConfig> {
        return appConfigRepository.config
    }
}