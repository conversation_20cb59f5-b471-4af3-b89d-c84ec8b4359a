package dev.paypass.domain.config.remote.use_case

import dev.paypass.domain.auth.AuthRepository
import dev.paypass.domain.config.remote.LoadConfigScheduler
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterNotNull

internal class StartPeriodicLoadConfigUseCaseImpl(
    private val authRepository: AuthRepository,
    private val loadConfigScheduler: LoadConfigScheduler,
) : StartPeriodicLoadConfigUseCase {

    override suspend fun invoke() {
        authRepository.authInfoChanges()
            .filterNotNull()
            .filter { authInfo ->
                authInfo.id.isNotEmpty() && authInfo.token.isNotEmpty()
            }
            .distinctUntilChanged()
            .collect { authInfo ->
                loadConfigScheduler.schedule(authInfo)
            }
    }
}