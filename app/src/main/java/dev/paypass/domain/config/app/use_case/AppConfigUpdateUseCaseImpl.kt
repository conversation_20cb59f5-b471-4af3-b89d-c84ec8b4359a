package dev.paypass.domain.config.app.use_case

import dev.paypass.domain.config.app.AppConfigRepository
import dev.paypass.domain.config.app.AppConfig

internal class AppConfigUpdateUseCaseImpl(
    private val appConfigRepository: AppConfigRepository
) : AppConfigUpdateUseCase {

    override suspend fun invoke(block: (AppConfig) -> AppConfig) {
        val updatedConfig = block(appConfigRepository.config.value)
        appConfigRepository.updateConfig(updatedConfig)
    }
}