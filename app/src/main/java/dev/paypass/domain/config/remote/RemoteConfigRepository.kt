package dev.paypass.domain.config.remote

import kotlinx.coroutines.flow.Flow

interface RemoteConfigRepository {

    suspend fun getRemoteConfigOrNull(): RemoteConfig?

    suspend fun getExpectedPackagesOrNull(): ExpectedPackages?

    fun remoteConfigChanges(): Flow<RemoteConfig>

    fun expectedPackagesChanges(): Flow<ExpectedPackages>

    suspend fun loadRemoteConfig(
        phoneNumber: String,
        token: String,
        appVersion: String
    ): Result<RemoteConfig>

    suspend fun loadExpectedPackages(): Result<ExpectedPackages>

    suspend fun saveRemoteConfig(remoteConfig: RemoteConfig): Result<Unit>
}