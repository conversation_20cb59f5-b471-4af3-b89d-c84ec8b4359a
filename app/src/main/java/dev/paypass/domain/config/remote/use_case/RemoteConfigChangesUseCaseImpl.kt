package dev.paypass.domain.config.remote.use_case

import dev.paypass.domain.config.remote.RemoteConfig
import dev.paypass.domain.config.remote.RemoteConfigRepository
import kotlinx.coroutines.flow.Flow

internal class RemoteConfigChangesUseCaseImpl(
    private val remoteConfigRepository: RemoteConfigRepository
) : RemoteConfigChangesUseCase {

    override fun invoke(): Flow<RemoteConfig> {
        return remoteConfigRepository.remoteConfigChanges()
    }
}