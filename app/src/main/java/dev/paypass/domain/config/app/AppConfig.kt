package dev.paypass.domain.config.app

import kotlinx.serialization.Serializable

@Serializable
data class AppConfig(
    val remoteLogEnabled: Boolean,
    val cancelSentNotifications: <PERSON>olean,
    val keepScreenOn: Boolean
) {

    companion object {
        val Default = AppConfig(
            remoteLogEnabled = false,
            cancelSentNotifications = true,
            keepScreenOn = false
        )
    }
}