package dev.paypass.domain.debug

import dev.paypass.domain.auth.AuthRepository
import dev.paypass.domain.config.app.AppConfigRepository
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.map
import timber.log.Timber

internal class RemoteLoggingByConfigUseCaseImpl(
    private val authRepository: AuthRepository,
    private val appConfigRepository: AppConfigRepository,
    private val remoteLoggers: List<RemoteLogger>
) : RemoteLoggingByConfigUseCase {

    override suspend fun invoke() {
        coroutineScope {
            async {
                appConfigRepository.config
                    .filterNotNull()
                    .map { it.remoteLogEnabled }
                    .distinctUntilChanged()
                    .collectLatest { enable ->
                        if (enable) {
                            remoteLoggers.forEach { Timber.Forest.plant(it.timberTree) }
                        } else {
                            Timber.Forest.uprootAll()
                            Timber.Forest.plant(Timber.DebugTree())
                        }
                        Timber.Forest.d("Remote log enable: $enable")
                    }
            }

            async {
                authRepository.authInfoChanges()
                    .filterNotNull()
                    .collectLatest { authInfo ->
                        setLogsIdentifier(authInfo.id)
                    }
            }
        }
    }

    private fun setLogsIdentifier(identifier: String) {
        remoteLoggers.forEach { logger ->
            logger.setIdentifier(identifier)
        }
    }
}