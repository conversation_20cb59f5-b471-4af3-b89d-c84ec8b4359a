package dev.paypass.domain.auth.use_case

import dev.paypass.system.permission.Permission
import dev.paypass.system.permission.PermissionsManager
import dev.paypass.system.thirdparty.QrScanLauncher
import dev.paypass.system.thirdparty.QrScanResult
import kotlinx.serialization.json.Json

internal class AuthScanQrUseCaseImpl(
    private val permissionsManager: PermissionsManager,
    private val qrScanLauncher: QrScanLauncher
) : AuthScanQrUseCase {

    private val json = Json {
        ignoreUnknownKeys = true
    }

    override suspend fun invoke(): AuthScanQrResult {
        val cameraPermissionStatue = permissionsManager.requestPermission(Permission.CAMERA)

        return if (cameraPermissionStatue.isGranted.not()) {
            AuthScanQrResult.MissingPermission(
                isPermissionPermanentlyDenied = cameraPermissionStatue.isDeniedRationale
            )
        } else {
            when (val qrResult = qrScanLauncher.launchQrScan()) {
                is QrScanResult.Success -> {
                    try {
                        val qrConfig = json.decodeFromString<QrConfigData>(qrResult.rawValue)
                        AuthScanQrResult.ConfigData(qrConfig = qrConfig)
                    } catch (e: Exception) {
                        AuthScanQrResult.Error(Exception("Неверный формат QR кода: ${e.message}"))
                    }
                }

                QrScanResult.UserCanceled -> AuthScanQrResult.UserCanceled

                QrScanResult.MissingPermission -> AuthScanQrResult.MissingPermission(
                    isPermissionPermanentlyDenied = true
                )

                is QrScanResult.Error -> AuthScanQrResult.Error(qrResult.exception)
            }
        }
    }
}