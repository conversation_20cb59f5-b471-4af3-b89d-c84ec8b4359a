package dev.paypass.domain.auth.use_case

import dev.paypass.BuildConfig
import dev.paypass.data.auth.error.UserNotFoundException
import dev.paypass.domain.auth.AuthInfo
import dev.paypass.domain.auth.AuthRepository
import dev.paypass.domain.config.remote.RemoteConfigRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

internal class IsAuthChangesUseCaseImpl(
    private val authRepository: AuthRepository,
    private val remoteConfigRepository: RemoteConfigRepository
) : IsAuthChangesUseCase {

    override fun invoke(): Flow<Boolean> {
        return authRepository.authInfoChanges().map { authInfo ->
            authInfo != null && ensureIsAuth(authInfo)
        }
    }

    private suspend fun ensureIsAuth(authInfo: AuthInfo): Boolean {
        val remoteConfig = remoteConfigRepository.getRemoteConfigOrNull()

        if (remoteConfig == null) {
            val remoteConfigResult = remoteConfigRepository.loadRemoteConfig(
                phoneNumber = authInfo.id,
                token = authInfo.token,
                appVersion = BuildConfig.VERSION_NAME
            )

            when {
                remoteConfigResult.isSuccess -> {
                    authRepository.saveAuth(authInfo.id, authInfo.token)
                    return true
                }

                else -> {
                    if (remoteConfigResult.exceptionOrNull() is UserNotFoundException) {
                        authRepository.clearAuth()
                    }
                    return false
                }
            }
        } else {
            return true
        }
    }
}