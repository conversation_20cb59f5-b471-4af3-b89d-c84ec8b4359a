package dev.paypass.domain.auth.use_case

import dev.paypass.domain.auth.AuthRepository
import dev.paypass.domain.config.remote.RemoteConfigRepository

internal class AuthLoginUseCaseImpl(
    private val remoteConfigRepository: RemoteConfigRepository,
    private val authRepository: AuthRepository
) : AuthLoginUseCase {

    override suspend fun invoke(
        id: String,
        token: String
    ): Result<Unit> {
        // Проверяем, что конфигурация уже сохранена
        val remoteConfig = remoteConfigRepository.getRemoteConfigOrNull()

        return if (remoteConfig != null) {
            try {
                // Сохраняем только ID пользователя, токен больше не нужен
                authRepository.saveAuth(id, "")
                Result.success(Unit)
            } catch (e: Exception) {
                Result.failure(e)
            }
        } else {
            Result.failure(Exception("Конфигурация не найдена. Отсканируйте QR код."))
        }
    }
}