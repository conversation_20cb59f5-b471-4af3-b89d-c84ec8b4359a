package dev.paypass.domain.auth.use_case

import dev.paypass.domain.config.remote.RemoteConfig
import dev.paypass.domain.config.remote.RemoteConfigRepository

internal class AuthSaveConfigUseCaseImpl(
    private val remoteConfigRepository: RemoteConfigRepository
) : AuthSaveConfigUseCase {

    override suspend fun invoke(qrConfig: QrConfigData): Result<Unit> {
        val remoteConfig = RemoteConfig(
            urlForPush = qrConfig.UrlForPush,
            urlForSms = qrConfig.UrlForSms,
            urlForHeartbeat = qrConfig.UrlAppExcept
        )

        return remoteConfigRepository.saveRemoteConfig(remoteConfig)
    }
}
