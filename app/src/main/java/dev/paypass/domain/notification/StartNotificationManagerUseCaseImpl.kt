package dev.paypass.domain.notification

import android.os.Build
import com.arkivanov.essenty.lifecycle.Lifecycle
import dev.paypass.system.activity.CurrentActivityProvider
import dev.paypass.system.device.DeviceManager
import dev.paypass.system.notification.SystemNotificationsManager
import dev.paypass.system.permission.Permission
import dev.paypass.system.permission.PermissionsManager
import dev.paypass.ui.util.stateFlow
import kotlinx.coroutines.flow.first

internal class StartNotificationManagerUseCaseImpl(
    private val currentActivityProvider: CurrentActivityProvider,
    private val permissionsManager: PermissionsManager,
    private val systemNotificationsManager: SystemNotificationsManager,
    private val deviceManager: DeviceManager,
) : StartNotificationManagerUseCase {

    override suspend fun invoke() {
        // wait for notification permission
        waitGrantNotificationPermission()
        //systemNotificationsManager.start()

        // wait for ignoring battery optimization
        deviceManager.isIgnoringBatteryOptimizations.first { isEnabled -> isEnabled }
        deviceManager.acquireWakeLock()
    }

    private suspend fun waitGrantNotificationPermission() {
        while (isNotificationListenerEnabled().not() || isNotificationPermissionGranted().not()) {
            currentActivityProvider.lifecycle.stateFlow().first { it == Lifecycle.State.RESUMED }
        }
    }

    private fun isNotificationListenerEnabled(): Boolean {
        return systemNotificationsManager.isNotificationListenerEnabled()
    }

    private fun isNotificationPermissionGranted(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            permissionsManager.checkPermission(Permission.POST_NOTIFICATIONS).isGranted
        } else {
            true
        }
    }
}