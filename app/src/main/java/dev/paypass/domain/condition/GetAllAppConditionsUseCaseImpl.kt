package dev.paypass.domain.condition

import android.os.Build
import com.arkivanov.essenty.lifecycle.Lifecycle
import dev.paypass.system.activity.CurrentActivityProvider
import dev.paypass.system.device.DeviceManager
import dev.paypass.system.location.LocationManager
import dev.paypass.system.notification.SystemNotificationsManager
import dev.paypass.system.permission.Permission
import dev.paypass.system.permission.PermissionsManager
import dev.paypass.ui.util.stateFlow
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map

internal class GetAllAppConditionsUseCaseImpl(
    private val currentActivityProvider: CurrentActivityProvider,
    private val permissionsManager: PermissionsManager,
    private val systemNotificationsManager: SystemNotificationsManager,
    private val locationManager: LocationManager,
    private val deviceManager: DeviceManager
) : GetAllAppConditionsUseCase {

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun invoke(): Flow<Map<AppCondition, Boolean>> {
        return currentActivityProvider.lifecycle.stateFlow()
            .filter { it == Lifecycle.State.RESUMED }
            .flatMapLatest {
                locationManager.isGpsEnabled.map { isGpsEnabled ->
                    buildConditions(isGpsEnabled)
                }
            }
    }

    private fun buildConditions(
        isGpsEnabled: Boolean
    ): Map<AppCondition, Boolean> {
        return buildMap {
            put(
                AppCondition.Permission.READ_SMS,
                permissionsManager.checkPermission(Permission.READ_SMS).isGranted
            )
            put(
                AppCondition.Permission.RECEIVE_SMS,
                permissionsManager.checkPermission(Permission.RECEIVE_SMS).isGranted
            )

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                put(
                    AppCondition.Permission.POST_NOTIFICATIONS,
                    permissionsManager.checkPermission(Permission.POST_NOTIFICATIONS).isGranted
                )
            }

            put(
                AppCondition.EnableNotificationListener,
                systemNotificationsManager.isNotificationListenerEnabled()
            )

            put(
                AppCondition.Permission.LOCATION,
                permissionsManager.checkPermission(Permission.ACCESS_FINE_LOCATION).isGranted
            )

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                put(
                    AppCondition.Permission.BACKGROUND_LOCATION,
                    permissionsManager.checkPermission(Permission.ACCESS_BACKGROUND_LOCATION).isGranted
                )
            }

            put(AppCondition.GpsEnabled, isGpsEnabled)

            put(AppCondition.IgnoreBatteryOptimization, deviceManager.isIgnoringBatteryOptimizations.value)
        }
    }
}