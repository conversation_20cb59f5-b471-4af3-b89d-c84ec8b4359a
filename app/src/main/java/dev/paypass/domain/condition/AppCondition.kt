package dev.paypass.domain.condition

sealed interface AppCondition {

    val id: Int

    enum class Permission(override val id: Int) : AppCondition {
        READ_SMS(1),
        RECEIVE_SMS(2),
        POST_NOTIFICATIONS(3),
        LOCATION(4),
        BACKGROUND_LOCATION(5)
    }

    data object EnableNotificationListener : AppCondition {
        override val id: Int = 6
    }

    data object GpsEnabled : AppCondition {
        override val id: Int = 7
    }

    data object IgnoreBatteryOptimization : AppCondition {
        override val id: Int = 8
    }
}