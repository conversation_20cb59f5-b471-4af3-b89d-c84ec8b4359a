package dev.paypass.domain.event.use_case

import dev.paypass.domain.Event
import dev.paypass.domain.event.EventRepository
import dev.paypass.system.sms.SmsManager
import timber.log.Timber

internal class SaveSmsEventsUseCaseImpl(
    private val smsManager: SmsManager,
    private val eventRepository: EventRepository
) : SaveSmsEventsUseCase {

    override suspend fun invoke() {
        smsManager.incomingSms
            .collect { incomingSms ->
                val event = Event.Sms(
                    id = incomingSms.hashCode().toLong(),
                    message = incomingSms.message,
                    timestamp = incomingSms.timestamp,
                    phoneNumber = incomingSms.sender,
                    status = Event.Status.New
                )

                Timber.d("SaveSmsEventsUseCaseImpl event: $event")

                eventRepository.saveEvent(event)
            }
    }
}