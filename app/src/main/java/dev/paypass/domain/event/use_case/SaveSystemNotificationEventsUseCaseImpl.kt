package dev.paypass.domain.event.use_case

import dev.paypass.core.analytic.AppAction
import dev.paypass.core.analytic.Logger
import dev.paypass.core.analytic.error
import dev.paypass.domain.Event
import dev.paypass.domain.config.remote.ExpectedPackages
import dev.paypass.domain.config.remote.RemoteConfigRepository
import dev.paypass.domain.config.app.AppConfigRepository
import dev.paypass.domain.event.EventRepository
import dev.paypass.system.notification.ReceivedNotification
import dev.paypass.system.notification.SystemNotificationsManager
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.suspendCancellableCoroutine

class SaveSystemNotificationEventsUseCaseImpl(
    private val systemNotificationsManager: SystemNotificationsManager,
    private val eventRepository: EventRepository,
    private val remoteConfigRepository: RemoteConfigRepository,
    private val debugManager: AppConfigRepository,
    private val logger: Logger
) : SaveSystemNotificationEventsUseCase,
    SystemNotificationsManager.Listener {

    private var expectedPackages: ExpectedPackages? = null

    override suspend fun invoke() {
        systemNotificationsManager.addListener(this)

        coroutineScope {
            async {
                suspendCancellableCoroutine<Unit> {
                    it.invokeOnCancellation {
                        systemNotificationsManager.removeListener(this@SaveSystemNotificationEventsUseCaseImpl)
                    }
                }
            }

            async {
                remoteConfigRepository.expectedPackagesChanges().collect { expectedPackages ->
                    <EMAIL> = expectedPackages
                }
            }
        }
    }

    override fun onNotificationReceived(receivedNotifications: List<ReceivedNotification>) {
        receivedNotifications.forEach { receivedNotification ->
            val expectedPackages = this.expectedPackages

            if (expectedPackages != null && expectedPackages.list.contains(receivedNotification.packageName)) {
                return@forEach
            }

            val event = Event.Notification(
                id = receivedNotification.id.toLong(),
                message = receivedNotification.text,
                timestamp = receivedNotification.timestamp,
                title = receivedNotification.title,
                packageName = receivedNotification.packageName,
                status = Event.Status.New
            )

            try {
                logger.log(AppAction.OnEventHandled(event))

                eventRepository.saveEventAsync(event)

                if (debugManager.config.value.cancelSentNotifications) {
                    systemNotificationsManager.cancelNotification(receivedNotification.key)
                }
            } catch (e: Exception) {
                logger.error(e, "Failed to save event")
            }
        }
    }
}