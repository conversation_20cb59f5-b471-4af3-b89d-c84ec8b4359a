package dev.paypass.domain.event.use_case

import dev.paypass.domain.Event
import dev.paypass.domain.auth.AuthInfo
import dev.paypass.domain.auth.AuthRepository
import dev.paypass.domain.config.remote.RemoteConfig
import dev.paypass.domain.config.remote.RemoteConfigRepository
import dev.paypass.domain.event.EventRepository
import dev.paypass.domain.event.SendEventScheduler
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.scan

internal class SendNotProcessedEventsUseCaseImpl(
    private val authRepository: AuthRepository,
    private val configRepository: RemoteConfigRepository,
    private val eventRepository: EventRepository,
    private val sendEventScheduler: SendEventScheduler
) : SendNotProcessedEventsUseCase {

    override suspend fun invoke() {
        combine(
            authRepository.authInfoChanges().filterNotNull(),
            configRepository.remoteConfigChanges(),
            eventRepository.notProcessedEvents()
        ) { authInfo, config, events ->
            Triple(authInfo, config, events)
        }
            .scan<Triple<AuthInfo, RemoteConfig, List<Event>>, Triple<AuthInfo, RemoteConfig, List<Event>>?>(null) { old, current ->
                val (authInfo, config, events) = current

                val authChanged = old?.first?.let { it != authInfo } ?: false
                val configChanged = old?.second?.let { it != config } ?: false
                val oldEvents = old?.third ?: emptyList()
                val newOrChangedEvents = events.filterNot(oldEvents::contains)

                // Если auth или config поменялись, или появились новые/изменённые эвенты
                if (authChanged || configChanged || newOrChangedEvents.isNotEmpty()) {
                    newOrChangedEvents.forEach { event ->
                        sendEventScheduler.schedule(
                            userId = authInfo.id,
                            userToken = authInfo.token,
                            url = when (event) {
                                is Event.Notification -> config.urlForPush
                                is Event.Sms -> config.urlForSms
                            },
                            event = event
                        )
                    }
                }

                current // Возвращаем текущие значения, чтобы в след. итерации они стали "старым" состоянием
            }
            .collect()
    }
}