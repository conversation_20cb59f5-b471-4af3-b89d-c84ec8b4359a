package dev.paypass.core.analytic

sealed interface LogEvent {

    val message: String

    interface Error : LogEvent {
        val error: Throwable

        companion object {
            operator fun invoke(
                error: Throwable,
                message: String? = null
            ): Error = object : Error {
                override val error: Throwable = error
                override val message: String = message ?: error.message ?: ""
            }
        }
    }

    interface Action : LogEvent {

        val name: String

        companion object {
            operator fun invoke(message: String): Action = object : Action {
                override val name: String = "common_action"
                override val message: String = message
            }
        }
    }
}