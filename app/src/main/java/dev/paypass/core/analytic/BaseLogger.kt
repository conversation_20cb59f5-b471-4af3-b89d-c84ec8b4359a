package dev.paypass.core.analytic

internal object BaseLogger : <PERSON><PERSON>, <PERSON>ggerRegistry {
    private val loggers = mutableSetOf<Logger>()

    override fun log(event: LogEvent) {
        loggers.forEach { it.log(event) }
    }

    override fun register(logger: Logger) {
        loggers.add(logger)
    }

    override fun unregister(logger: Logger) {
        loggers.remove(logger)
    }
}