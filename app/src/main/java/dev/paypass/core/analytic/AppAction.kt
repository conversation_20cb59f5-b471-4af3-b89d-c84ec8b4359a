package dev.paypass.core.analytic

import dev.paypass.domain.Event

sealed interface AppAction : LogEvent.Action {

    data object Start : AppAction {
        override val name: String = "app_start"
        override val message: String = "App started"
    }

    class OnEventHandled(
        event: Event,
        override val name: String = "event_handled",
        override val message: String = "Event handled. event: $event"
    ) : AppAction

    sealed interface OnEventSend {

        class Scheduled(
            event: Event,
            isFirstAttempt: Boolean,
            override val name: String = "event_send_scheduled",
            override val message: String = "Event send scheduled. isFirstAttempt: $isFirstAttempt event: $event"
        ) : AppAction

        class Success(
            event: Event,
            override val name: String = "event_send_success",
            override val message: String = "Event send success. event: $event"
        ) : AppAction

        class Failure(
            event: Event?,
            error: Throwable?,
            attempt: Int,
            override val name: String = "event_send_failure",
            override val message: String = "Event send failure. attempt: $attempt event: $event error: $error"
        ) : AppAction
    }

    sealed interface OnHeartbeatSend {

        data object Success : AppAction {
            override val name: String = "heartbeat_send_success"
            override val message: String = "Heartbeat send success"
        }

        class Failure(
            error: Throwable?,
            override val name: String = "heartbeat_send_failure",
            override val message: String = "Heartbeat send failure. error: $error"
        ) : AppAction
    }
}