package dev.paypass

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import dev.paypass.domain.startup.StartupUseCase
import org.koin.core.component.KoinComponent

class BootReceiver : BroadcastReceiver(), KoinComponent {

    override fun onReceive(context: Context, intent: Intent) {
        if (intent.action == Intent.ACTION_BOOT_COMPLETED) {
            val startupUseCase = StartupUseCase.newInstance()

            startupUseCase(context)
        }
    }
}