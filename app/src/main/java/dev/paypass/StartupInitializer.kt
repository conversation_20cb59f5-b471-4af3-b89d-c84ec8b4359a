package dev.paypass

import android.content.Context
import androidx.startup.Initializer
import dev.paypass.domain.startup.StartupUseCase

@Suppress("unused") // declared in AndroidManifest.xml and used by the system
class StartupInitializer : Initializer<Context> {

    override fun create(context: Context): Context {
        val startup = StartupUseCase.newInstance()

        startup(context)

        return context
    }

    override fun dependencies(): List<Class<out Initializer<*>>> {
        return emptyList()
    }
}