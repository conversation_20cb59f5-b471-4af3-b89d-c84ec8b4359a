package dev.paypass.system.sms

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.provider.Telephony
import timber.log.Timber

internal class IncomingSmsBroadcastReceiver : BroadcastReceiver() {

    override fun onReceive(context: Context, intent: Intent) {
        if (intent.action != Telephony.Sms.Intents.SMS_RECEIVED_ACTION) return
        val smsReceiver = smsReceiver ?: return

        val smsMessages = Telephony.Sms.Intents.getMessagesFromIntent(intent)

        Timber.d("Received ${smsMessages.size} SMS messages")

        smsMessages.forEach { smsMessage ->
            val incomingSms = IncomingSms(
                sender = smsMessage.displayOriginatingAddress,
                message = smsMessage.displayMessageBody,
                timestamp = smsMessage.timestampMillis
            )

            smsReceiver.onSmsReceived(incomingSms)
        }
    }

    companion object {
        var smsReceiver: IncomingSmsReceiver? = null

        fun register(context: Context, smsReceiver: IncomingSmsReceiver) {
            this.smsReceiver = smsReceiver

            context.registerReceiver(
                IncomingSmsBroadcastReceiver(),
                IntentFilter(Telephony.Sms.Intents.SMS_RECEIVED_ACTION)
            )
        }
    }
}