package dev.paypass.system.sms

import com.arkivanov.essenty.lifecycle.doOnStart
import dev.paypass.system.activity.CurrentActivityProvider
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import timber.log.Timber

internal class SmsManagerImpl(
    private val currentActivityProvider: CurrentActivityProvider
) : SmsManager {

    private val _incomingSms = MutableSharedFlow<IncomingSms>()
    override val incomingSms = _incomingSms.asSharedFlow()

    init {
        currentActivityProvider.lifecycle.doOnStart {
            Timber.d("Registering incoming SMS receiver")
            val currentActivity = currentActivityProvider.currentActivity!!
            IncomingSmsBroadcastReceiver.register(currentActivity.applicationContext, incomingSmsReceiver)
        }
    }

    private val incomingSmsReceiver = object : IncomingSmsReceiver {
        override fun onSmsReceived(sms: IncomingSms) {
            Timber.d("onSmsReceived: $sms")
            GlobalScope.launch { _incomingSms.emit(sms) }
        }
    }
}