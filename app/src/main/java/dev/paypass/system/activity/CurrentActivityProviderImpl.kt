package dev.paypass.system.activity

import android.app.Activity
import android.app.Application
import android.content.Context
import androidx.activity.ComponentActivity
import com.arkivanov.essenty.lifecycle.Lifecycle
import com.arkivanov.essenty.lifecycle.LifecycleRegistry
import com.arkivanov.essenty.lifecycle.pause
import com.arkivanov.essenty.lifecycle.resume
import com.arkivanov.essenty.lifecycle.start
import dev.paypass.system.util.DefaultActivityLifecycleCallbacks
import java.lang.ref.WeakReference

internal class CurrentActivityProviderImpl(
    context: Context
) : CurrentActivityProvider {

    private var _currentActivity = WeakReference<ComponentActivity>(null)

    private val lifecycleRegistry = LifecycleRegistry()
    override val lifecycle = lifecycleRegistry

    override val currentActivity
        get() = _currentActivity.get()

    private val callbacks = object : DefaultActivityLifecycleCallbacks {

        override fun onActivityStarted(activity: Activity) {
            (activity as? ComponentActivity)?.let {
                _currentActivity = WeakReference(it)
            }
        }

        override fun onActivityPostStarted(activity: Activity) {
            lifecycleRegistry.start()
        }

        override fun onActivityResumed(activity: Activity) {
            (activity as? ComponentActivity)?.let {
                _currentActivity = WeakReference(it)
            }
        }

        override fun onActivityPostResumed(activity: Activity) {
            lifecycleRegistry.resume()
        }

        override fun onActivityPostPaused(activity: Activity) {
            lifecycleRegistry.pause()
        }
    }

    init {
        val app = context.applicationContext as? Application
            ?: error("Application context not found")

        app.registerActivityLifecycleCallbacks(callbacks)
    }
}