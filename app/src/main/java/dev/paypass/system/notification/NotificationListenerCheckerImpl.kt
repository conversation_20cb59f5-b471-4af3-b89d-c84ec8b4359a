@file:OptIn(ExperimentalUuidApi::class)

package dev.paypass.system.notification

import android.Manifest
import android.app.Notification
import android.app.NotificationManager
import android.content.Context
import android.content.pm.PackageManager
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationChannelCompat
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import dev.paypass.R
import dev.paypass.system.notification.NotificationListenerChecker.ListeningStatus
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withTimeout
import kotlin.coroutines.resume
import kotlin.time.Duration.Companion.seconds
import kotlin.uuid.ExperimentalUuidApi
import kotlin.uuid.Uuid

internal class NotificationListenerCheckerImpl(
    private val systemNotificationsManager: SystemNotificationsManager,
    private val context: Context
) : NotificationListenerChecker {

    private val mutableListeningStatus = MutableStateFlow(ListeningStatus.NOT_LISTENING)
    override val listeningStatus = mutableListeningStatus.asStateFlow()

    override suspend fun check(): Boolean {
        mutableListeningStatus.value = ListeningStatus.CHECKING

        val notificationManager = NotificationManagerCompat.from(context)

        val channel = createNotificationChannel()

        val notificationText = "Проверка наличия слушателя уведомлений. ${Uuid.random()}"
        val notification = createNotification(notificationText)

        notificationManager.createNotificationChannel(channel)

        if (ActivityCompat.checkSelfPermission(
                context,
                Manifest.permission.POST_NOTIFICATIONS
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            return false
        }

        val result = runCatching {
            withTimeout(2.seconds) {
                suspendCancellableCoroutine { continuation ->
                    val listener = object : SystemNotificationsManager.Listener {
                        override fun onNotificationReceived(receivedNotification: List<ReceivedNotification>) {
                            val checkNotification =
                                receivedNotification.firstOrNull { it.text == notificationText }

                            if (checkNotification != null) {
                                systemNotificationsManager.removeListener(this)
                                systemNotificationsManager.cancelNotification(checkNotification.key)

                                if (continuation.isActive) {
                                    continuation.resume(true)
                                }
                            }
                        }
                    }

                    systemNotificationsManager.addListener(listener)

                    continuation.invokeOnCancellation {
                        systemNotificationsManager.removeListener(listener)
                    }

                    notificationManager.notify(NOTIFICATION_ID, notification)
                }
            }
        }

        mutableListeningStatus.value = if (result.isSuccess) {
            ListeningStatus.LISTENING
        } else {
            ListeningStatus.NOT_LISTENING
        }

        return result.isSuccess
    }

    private fun createNotificationChannel(): NotificationChannelCompat {
        return NotificationChannelCompat.Builder(CHANNEL_ID, NotificationManager.IMPORTANCE_LOW)
            .setName("App Update Download")
            .build()
    }

    private fun createNotification(text: String): Notification {
        return NotificationCompat.Builder(context, CHANNEL_ID)
            .setContentTitle("PayPass")
            .setContentText(text)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setAutoCancel(false)
            .build()
    }

    companion object {
        const val CHANNEL_ID = "Heartbeat"
        const val NOTIFICATION_ID = 10_000
    }
}