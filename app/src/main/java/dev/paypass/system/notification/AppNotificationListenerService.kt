package dev.paypass.system.notification

import android.annotation.SuppressLint
import android.app.Notification
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.os.IBinder
import android.service.notification.NotificationListenerService
import android.service.notification.StatusBarNotification
import androidx.core.app.NotificationChannelCompat
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import dev.paypass.R
import dev.paypass.system.util.setComponentEnabledSetting
import dev.paypass.ui.MainActivity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.security.MessageDigest
import java.util.ArrayList
import kotlin.time.measureTimedValue

@SuppressLint("WakelockTimeout")
class AppNotificationListenerService : NotificationListenerService() {

    private val scope = CoroutineScope(Dispatchers.Main.immediate + SupervisorJob())

    private val mutex = Mutex()

    private val sentReceivedNotificationIds = mutableSetOf<Int>()

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_CANCEL_NOTIFICATION -> {
                val key = intent.getStringExtra(EXTRA_NOTIFICATION_KEY)

                if (key != null) {
                    scope.launch {
                        try {
                            mutex.withLock { processCancelNotification(key) }
                        } catch (e: Exception) {
                            Timber.e(e, "Failed to cancel notification")
                        }
                    }
                }
            }

            ACTION_RECONNECT -> {
                Timber.d("onStartCommand: ACTION_RECONNECT")
                tryReconnectService()
            }
        }

        return START_STICKY
    }

    override fun onNotificationPosted(sbn: StatusBarNotification, rankingMap: RankingMap) {
        scope.launch {
            try {
                mutex.withLock { processNewNotification(sbn) }
            } catch (e: Exception) {
                Timber.e(e, "Failed to process notification")
            }
        }
    }

    private suspend fun processCancelNotification(key: String) {
        withContext(Dispatchers.Default) {
            val activeReceivedNotificationsForKey = activeNotifications
                .flatMap { it.extractReceivedNotifications() }
                .filter { it.key == key }

            if (activeReceivedNotificationsForKey.isNotEmpty()) {
                withContext(Dispatchers.Main.immediate) {
                    cancelNotification(key)
                }

                activeReceivedNotificationsForKey.forEach { sentReceivedNotificationIds.remove(it.id) }

                Timber.d("onStartCommand: cancelNotification: $key remove: ${activeReceivedNotificationsForKey.size} size: ${sentReceivedNotificationIds.size}")
            }
        }
    }

    private suspend fun processNewNotification(sbn: StatusBarNotification) {
        withContext(Dispatchers.Default) {
            val (debugMessage, processTime) = measureTimedValue {
                // extract new received notifications
                val newReceivedNotifications = sbn.extractReceivedNotifications()
                    .filterNot { receivedNotification ->
                        sentReceivedNotificationIds.contains(receivedNotification.id)
                    }

                if (newReceivedNotifications.isEmpty()) {
                    Timber.d("processNotification: no new notifications")
                    return@withContext
                }

                // save new received notifications
                newReceivedNotifications.forEach { receivedNotification ->
                    sentReceivedNotificationIds.add(receivedNotification.id)
                }

                val intent = Intent(ACTION_NOTIFICATION_POSTED).apply {
                    setPackage(packageName)
                    putParcelableArrayListExtra(
                        EXTRA_RECEIVED_NOTIFICATIONS,
                        ArrayList(newReceivedNotifications)
                    )
                }

                withContext(Dispatchers.Main.immediate) {
                    sendBroadcast(intent)
                }

                val packages = newReceivedNotifications.map { it.packageName }.distinct().joinToString()
                val titles = newReceivedNotifications.map { it.title }.distinct().joinToString()
                val messages = newReceivedNotifications.map { it.text }.distinct().joinToString()

                "count: ${newReceivedNotifications.size} packages: $packages, titles: $titles, messages: $messages"
            }

            Timber.d("processNotification: $debugMessage processTime: ${processTime.inWholeMilliseconds}")
        }
    }

    private fun StatusBarNotification.extractReceivedNotifications(): List<ReceivedNotification> {
        val title = NotificationCompat.getContentTitle(notification)
        val text = NotificationCompat.getContentText(notification)

        if ((notification.flags and Notification.FLAG_GROUP_SUMMARY) != 0) {
            return emptyList()
        }

        val messagingStyle =
            NotificationCompat.MessagingStyle.extractMessagingStyleFromNotification(notification)

        if (isGroup && messagingStyle != null && messagingStyle.messages.isNotEmpty()) {
            return messagingStyle.messages.mapNotNull { groupMessage ->
                val groupMessageText = groupMessage.text?.toString() ?: return@mapNotNull null
                ReceivedNotification(
                    id = customId(groupMessageText, groupMessage.timestamp.toString()),
                    key = key,
                    packageName = packageName,
                    title = title.toString(),
                    text = groupMessageText,
                    whenTime = groupMessage.timestamp,
                    timestamp = postTime
                )
            }
        } else if (text != null && text.isNotBlank()) {
            return listOf(
                ReceivedNotification(
                    id = customId(text.toString()),
                    key = key,
                    packageName = packageName,
                    title = title.toString(),
                    text = text.toString(),
                    whenTime = notification.`when`,
                    timestamp = postTime
                )
            )
        } else {
            Timber.d("extractReceivedNotifications: Ignore empty notification. packageName: ${packageName}, title: $title, text: $text")
            return emptyList()
        }
    }

    private fun StatusBarNotification.customId(vararg field: String = emptyArray()): Int {
        val id = "$packageName|$id|${tag.orEmpty()}|${field.joinToString("|")}"
        val idMD5 = MessageDigest.getInstance("MD5").digest(id.toByteArray())

        return idMD5.fold(0) { acc, byte ->
            (acc shl 8) or (byte.toInt() and 0xFF)
        }
    }

    override fun onListenerConnected() {
        Timber.d("onListenerConnected")

        startForeground(SERVICE_NOTIFICATION_ID, createServiceNotification())

        val activeNotifications = runCatching { activeNotifications }.getOrElse {
            Timber.e(it, "Failed to get active notifications")
            return
        }

        activeNotifications.flatMap { it.extractReceivedNotifications() }
            .forEach { receivedNotification ->
                Timber.d("markAsRead: packageName: ${receivedNotification.packageName}, id: ${receivedNotification.id}")
                sentReceivedNotificationIds.add(receivedNotification.id)
            }

        val intent = Intent(ACTION_STATUS_CHANGED).apply {
            setPackage(packageName)
            putExtra(EXTRA_STATUS_IS_CONNECTED, true)
        }
        sendBroadcast(intent)
    }

    override fun onListenerDisconnected() {
        Timber.d("onListenerDisconnected")

        val intent = Intent(ACTION_STATUS_CHANGED).apply {
            setPackage(packageName)
            putExtra(EXTRA_STATUS_IS_CONNECTED, false)
        }
        sendBroadcast(intent)
    }

    override fun onUnbind(intent: Intent?): Boolean {
        Timber.d("onUnbind: $intent")
        stopSelf()
        return super.onUnbind(intent)
    }

    override fun onBind(intent: Intent?): IBinder? {
        Timber.d("onBind: $intent")
        return super.onBind(intent)
    }

    override fun onDestroy() {
        Timber.d("onDestroy")
    }

    private fun createServiceNotification(): Notification {
        val notificationManager = NotificationManagerCompat.from(this)

        val channel = NotificationChannelCompat.Builder(
            SERVICE_NOTIFICATION_CHANNEL_ID,
            NotificationManager.IMPORTANCE_HIGH
        )
            .setName(SERVICE_NOTIFICATION_CHANNEL_NAME)
            .build()

        notificationManager.createNotificationChannel(channel)

        val activityIntent = Intent(this, MainActivity::class.java)

        val contentIntent = PendingIntent.getActivity(
            this,
            0,
            activityIntent,
            PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, SERVICE_NOTIFICATION_CHANNEL_ID)
            .setContentTitle("PayPass")
            .setContentText("Слушает уведомления...")
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentIntent(contentIntent)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setOngoing(true)
            .build()
    }

    private fun tryReconnectService() {
        Timber.d("Trying to reconnect...")
        val componentName = ComponentName(applicationContext, AppNotificationListenerService::class.java)
        toggleNotificationListenerService()
        requestRebind(componentName)
        stopSelf()
    }

    private fun toggleNotificationListenerService() {
        val componentName = ComponentName(applicationContext, AppNotificationListenerService::class.java)
        setComponentEnabledSetting(componentName, false)
        setComponentEnabledSetting(componentName, true)
    }

    companion object {
        private const val SERVICE_NOTIFICATION_ID = 1
        private const val SERVICE_NOTIFICATION_CHANNEL_ID = "PayPass"
        private const val SERVICE_NOTIFICATION_CHANNEL_NAME = "PayPass"

        const val ACTION_NOTIFICATION_POSTED = "ACTION_NOTIFICATION_POSTED"
        const val EXTRA_RECEIVED_NOTIFICATIONS = "EXTRA_RECEIVED_NOTIFICATIONS"

        const val ACTION_STATUS_CHANGED = "ACTION_CONNECT_STATUS_CHANGED"
        const val EXTRA_STATUS_IS_CONNECTED = "EXTRA_IS_CONNECTED"

        const val ACTION_CANCEL_NOTIFICATION = "ACTION_CANCEL_NOTIFICATION"
        const val EXTRA_NOTIFICATION_KEY = "EXTRA_NOTIFICATION_KEY"

        const val ACTION_RECONNECT = "ACTION_RECONNECT"

        fun tryReconnectService(context: Context) {
            val intent = Intent(context, AppNotificationListenerService::class.java).apply {
                action = ACTION_RECONNECT
            }
            context.startService(intent)
        }

        fun cancelNotification(context: Context, key: String) {
            val intent = Intent(context, AppNotificationListenerService::class.java).apply {
                action = ACTION_CANCEL_NOTIFICATION
                putExtra(EXTRA_NOTIFICATION_KEY, key)
            }
            context.startService(intent)
        }
    }
}