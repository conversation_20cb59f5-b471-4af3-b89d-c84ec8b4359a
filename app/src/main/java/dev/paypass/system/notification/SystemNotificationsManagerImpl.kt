package dev.paypass.system.notification

import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.provider.Settings
import androidx.core.content.ContextCompat
import androidx.core.content.IntentCompat
import dev.paypass.core.analytic.Logger
import dev.paypass.core.analytic.error
import dev.paypass.system.activity.CurrentActivityProvider
import dev.paypass.system.util.isServiceRunning
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.concurrent.CopyOnWriteArrayList

internal class SystemNotificationsManagerImpl(
    private val context: Context,
    private val currentActivityProvider: CurrentActivityProvider,
    private val logger: Logger
) : SystemNotificationsManager {

    private val scope = CoroutineScope(Dispatchers.Default + SupervisorJob())

    private val mutableIsRunning = MutableStateFlow(false)
    override val isRunning = mutableIsRunning.asStateFlow()

    private val listeners = CopyOnWriteArrayList<SystemNotificationsManager.Listener>()

    private val serviceReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            try {
                when (intent.action) {
                    AppNotificationListenerService.ACTION_NOTIFICATION_POSTED -> {
                        val receivedNotifications = IntentCompat.getParcelableArrayListExtra(
                            intent,
                            AppNotificationListenerService.EXTRA_RECEIVED_NOTIFICATIONS,
                            ReceivedNotification::class.java
                        )

                        if (receivedNotifications != null && receivedNotifications.isNotEmpty()) {
                            onNotificationReceived(receivedNotifications)
                        }
                    }

                    AppNotificationListenerService.ACTION_STATUS_CHANGED -> {
                        val isConnected = intent.getBooleanExtra(
                            AppNotificationListenerService.EXTRA_STATUS_IS_CONNECTED,
                            false
                        )
                        onServiceStatusChanged(isConnected)
                    }
                }
            } catch (e: Exception) {
                logger.error(e, "Failed to parse intent: $intent")
            }
        }
    }

    init {
        try {
            ContextCompat.registerReceiver(
                context,
                serviceReceiver,
                IntentFilter().apply {
                    addAction(AppNotificationListenerService.ACTION_NOTIFICATION_POSTED)
                    addAction(AppNotificationListenerService.ACTION_STATUS_CHANGED)
                },
                ContextCompat.RECEIVER_NOT_EXPORTED
            )
        } catch (_: Exception) {
            // Ignore
        }

        mutableIsRunning.value = isAlive()
    }

    override fun addListener(listener: SystemNotificationsManager.Listener) {
        listeners.add(listener)
    }

    override fun removeListener(listener: SystemNotificationsManager.Listener) {
        listeners.remove(listener)
    }

    override fun cancelNotification(key: String) {
        AppNotificationListenerService.cancelNotification(context, key)
    }

    override fun openNotificationListenerSettings() {
        val intent = Intent(Settings.ACTION_NOTIFICATION_LISTENER_SETTINGS)

        currentActivityProvider.currentActivity?.startActivity(intent)
    }

    override fun isNotificationListenerEnabled(): Boolean {
        return fetchAllNotificationListenerApps().any { appInfo ->
            appInfo.packageName == context.packageName
        }
    }

    override fun fetchAllNotificationListenerApps(): Sequence<AppNotificationListenerInfo> {
        val enabledNotificationListeners = Settings.Secure.getString(
            context.contentResolver,
            ENABLED_NOTIFICATION_LISTENERS
        ).orEmpty()

        val enabledNotificationListenerComponents = enabledNotificationListeners.split(":".toRegex())
            .asSequence()
            .mapNotNull { enabledNotificationListenerComponentName ->
                runCatching { ComponentName.unflattenFromString(enabledNotificationListenerComponentName) }.getOrNull()
            }

        return enabledNotificationListenerComponents.map { component ->
            val appInfo = runCatching { context.packageManager.getApplicationInfo(component.packageName, 0) }.getOrNull()
            val appLabel = runCatching { context.packageManager.getApplicationLabel(appInfo!!).toString() }.getOrNull()

            AppNotificationListenerInfo(
                packageName = component.packageName,
                appLabel = appLabel ?: "unknown",
            )
        }
    }

    private fun isAlive(): Boolean {
        return context.isServiceRunning<AppNotificationListenerService>()
    }

    private fun onNotificationReceived(receivedNotifications: List<ReceivedNotification>) {
        Timber.d("onNotificationReceived: $receivedNotifications")
        scope.launch {
            listeners.forEach { it.onNotificationReceived(receivedNotifications) }
        }
    }

    private fun onServiceStatusChanged(isConnected: Boolean) {
        Timber.d("onServiceStatusChanged: $isConnected")
        mutableIsRunning.value = isConnected

        if (isConnected) {
            if (isAlive()) {
                Timber.d("Notification listener service started")
            } else {
                val error = IllegalStateException("Notification listener service connected but it was not alive")
                logger.error(error)
                tryReconnect()
            }
        } else {
            Timber.d("Notification listener service stopped")
        }
    }

    override fun tryReconnect() {
        Timber.d("tryReconnect")
        AppNotificationListenerService.tryReconnectService(context)
    }

    companion object {
        private const val ENABLED_NOTIFICATION_LISTENERS = "enabled_notification_listeners"
    }
}