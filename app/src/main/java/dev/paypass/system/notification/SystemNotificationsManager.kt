package dev.paypass.system.notification

import kotlinx.coroutines.flow.StateFlow

interface SystemNotificationsManager {

    val isRunning: StateFlow<Boolean>

    fun addListener(listener: Listener)

    fun removeListener(listener: Listener)

    fun cancelNotification(key: String)

    fun openNotificationListenerSettings()

    fun isNotificationListenerEnabled(): Boolean

    fun fetchAllNotificationListenerApps(): Sequence<AppNotificationListenerInfo>

    fun tryReconnect()

    interface Listener {
        fun onNotificationReceived(receivedNotification: List<ReceivedNotification>)
    }
}