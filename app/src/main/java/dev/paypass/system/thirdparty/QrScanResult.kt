package dev.paypass.system.thirdparty

import java.lang.Exception

sealed interface QrScanResult {

    data class Success(
        val rawBytes: ByteArray,
        val rawValue: String
    ) : QrScanResult {
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false

            other as Success

            if (!rawBytes.contentEquals(other.rawBytes)) return false
            if (rawValue != other.rawValue) return false

            return true
        }

        override fun hashCode(): Int {
            var result = rawBytes.contentHashCode()
            result = 31 * result + rawValue.hashCode()
            return result
        }
    }

    data object UserCanceled : QrScanResult

    data object MissingPermission : QrScanResult

    data class Error(
        val exception: Exception
    ) : QrScanResult {

        val message: String
            get() = exception.message ?: ""
    }
}