package dev.paypass.system.thirdparty

import dev.paypass.system.activity.CurrentActivityProvider
import io.github.g00fy2.quickie.QRResult
import io.github.g00fy2.quickie.ScanQRCode
import kotlinx.coroutines.suspendCancellableCoroutine
import java.util.concurrent.atomic.AtomicInteger
import kotlin.coroutines.resume

internal class QrScanLauncherImpl(
    private val currentActivityProvider: CurrentActivityProvider
) : QrScanLauncher {

    private val keyIncrement = AtomicInteger(0)

    private val currentActivity
        get() = currentActivityProvider.currentActivity ?: error("No current activity")

    private val nextRequestKey: String
        get() = "qr_scan_${keyIncrement.getAndIncrement()}"

    override suspend fun launchQrScan(): QrScanResult {
        return suspendCancellableCoroutine { continuation ->
            val launcher = currentActivity.activityResultRegistry.register(
                nextRequestKey,
                ScanQRCode()
            ) { result ->
                if (continuation.isActive) {
                    continuation.resume(result.mapToQrScanResult())
                }
            }

            launcher.launch(null)

            continuation.invokeOnCancellation {
                launcher.unregister()
            }
        }
    }

    private fun QRResult.mapToQrScanResult(): QrScanResult {
        return when (this) {
            is QRResult.QRSuccess -> QrScanResult.Success(
                rawBytes = content.rawBytes ?: byteArrayOf(),
                rawValue = content.rawValue ?: ""
            )
            QRResult.QRUserCanceled -> QrScanResult.UserCanceled
            QRResult.QRMissingPermission -> QrScanResult.MissingPermission
            is QRResult.QRError -> QrScanResult.Error(this.exception)
        }
    }
}