package dev.paypass.system.location

import android.Manifest
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.location.Location
import android.os.CancellationSignal
import androidx.annotation.RequiresPermission
import androidx.core.content.getSystemService
import androidx.core.location.LocationManagerCompat
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.asExecutor
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

private typealias SystemLocationManager = android.location.LocationManager

internal class LocationManagerImpl(
    context: Context
) : LocationManager {

    private val locationManager = context.getSystemService<SystemLocationManager>()
        ?: error("LocationManager is null")

    private val mutableIsGpsEnabled = MutableStateFlow(false)
    override val isGpsEnabled = mutableIsGpsEnabled.asStateFlow()

    private val locationProviderChangesReceiver = object : BroadcastReceiver() {

        override fun onReceive(context: Context?, intent: Intent?) {
            checkGpsEnabled()
        }
    }

    init {
        context.registerReceiver(
            locationProviderChangesReceiver,
            IntentFilter(SystemLocationManager.PROVIDERS_CHANGED_ACTION)
        )
        checkGpsEnabled()
    }

    @OptIn(ExperimentalStdlibApi::class)
    @RequiresPermission(allOf = [Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION])
    override suspend fun getCurrentLocation(): Location {
        return withContext(Dispatchers.Main.immediate) {
            val executor = coroutineContext[CoroutineDispatcher]?.asExecutor() ?: error("Executor is null")

            suspendCancellableCoroutine { continuation ->
                val cancellationSignal = CancellationSignal()
                try {
                    LocationManagerCompat.getCurrentLocation(
                        locationManager,
                        SystemLocationManager.GPS_PROVIDER,
                        cancellationSignal,
                        executor,
                    ) { location ->
                        if (continuation.isActive) {
                            continuation.resume(location)
                        }
                    }
                } catch (ex: Exception) {
                    ex.printStackTrace()
                    continuation.resumeWithException(ex)
                }
                continuation.invokeOnCancellation { cancellationSignal.cancel() }
            }
        }
    }

    private fun checkGpsEnabled() {
        mutableIsGpsEnabled.value =
            locationManager.isProviderEnabled(SystemLocationManager.GPS_PROVIDER)
    }
}