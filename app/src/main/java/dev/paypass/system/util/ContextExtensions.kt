package dev.paypass.system.util

import android.app.ActivityManager
import android.content.ComponentName
import android.content.Context
import android.content.pm.PackageManager
import android.os.Process
import androidx.core.content.getSystemService

inline fun <reified T> Context.getComponentName() = ComponentName(this, T::class.java)

fun Context.setComponentEnabledSetting(
    componentName: ComponentName,
    enable: Boolean,
    flags: Int = PackageManager.DONT_KILL_APP
) {
    val newState = if (enable) {
        PackageManager.COMPONENT_ENABLED_STATE_ENABLED
    } else {
        PackageManager.COMPONENT_ENABLED_STATE_DISABLED
    }

    packageManager.setComponentEnabledSetting(componentName, newState, flags)
}

inline fun <reified T> Context.isServiceRunning(): Boolean {
    return isServiceRunning(getComponentName<T>())
}

@Suppress("DEPRECATION")
fun Context.isServiceRunning(componentName: ComponentName): Boolean {
    val activityManager = getSystemService<ActivityManager>() ?: return false

    return activityManager.getRunningServices(Integer.MAX_VALUE).any { info ->
        info.service == componentName && info.pid == Process.myPid()
    }
}