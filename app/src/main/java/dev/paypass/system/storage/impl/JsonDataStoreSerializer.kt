package dev.paypass.system.storage.impl

import androidx.datastore.core.CorruptionException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.StringFormat
import kotlinx.serialization.serializer
import java.io.InputStream
import java.io.OutputStream
import java.lang.reflect.Type

internal class JsonDataStoreSerializer<T: Any>(
    typeOfT: Type,
    private val stringFormat: StringFormat,
    override val defaultValue: T?
): DataStoreSerializer<T?> {

    @Suppress("UNCHECKED_CAST")
    private val kSerializer = serializer(typeOfT) as kotlinx.serialization.KSerializer<T>

    override suspend fun readFrom(input: InputStream): T {
        try {
            val bytes = input.readBytes()
            val string = bytes.decodeToString()

            return stringFormat.decodeFromString(kSerializer, string)
        } catch (e: Exception) {
            throw CorruptionException("Cannot read data", e)
        }
    }

    override suspend fun writeTo(t: T?, output: OutputStream) {
        val string = stringFormat.encodeToString(kSerializer, t ?: return)
        val bytes = string.encodeToByteArray()

        withContext(Dispatchers.IO) {
            output.write(bytes)
        }
    }

}