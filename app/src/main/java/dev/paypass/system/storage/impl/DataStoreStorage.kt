package dev.paypass.system.storage.impl

import android.content.Context
import androidx.datastore.core.handlers.ReplaceFileCorruptionHandler
import androidx.datastore.dataStore
import dev.paypass.system.storage.Storage
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull

internal class DataStoreStorage<T>(
    private val context: Context,
    dataStoreSerializer: DataStoreSerializer<T?>,
    key: String,
    defaultValue: T?
): Storage<T> {

    private val Context.dataStore by dataStore(
        fileName = key,
        serializer = dataStoreSerializer,
        corruptionHandler = ReplaceFileCorruptionHandler { defaultValue }
    )

    override fun changesFlow(): Flow<T?> {
        return context.dataStore.data
    }

    override suspend fun getOrNull(): T? {
        return context.dataStore.data.firstOrNull()
    }

    override suspend fun clear() {
        context.dataStore.updateData { null }
    }

    override suspend fun update(value: T) {
        context.dataStore.updateData { value }
    }
}