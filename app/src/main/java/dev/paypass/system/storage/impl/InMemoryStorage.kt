package dev.paypass.system.storage.impl

import dev.paypass.system.storage.Storage
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow

internal class InMemoryStorage<T>: Storage<T> {

    private val value = MutableStateFlow<T?>(null)

    override fun changesFlow(): Flow<T?> {
        return value.asStateFlow()
    }

    override suspend fun getOrNull(): T? {
        return value.value
    }

    override suspend fun clear() {
        value.value = null
    }

    override suspend fun update(value: T) {
        this.value.value = value
    }
}