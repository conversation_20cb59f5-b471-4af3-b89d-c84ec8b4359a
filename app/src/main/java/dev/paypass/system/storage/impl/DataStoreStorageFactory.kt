package dev.paypass.system.storage.impl

import android.content.Context
import dev.paypass.system.storage.Storage
import dev.paypass.system.storage.StorageFactory
import kotlinx.serialization.StringFormat
import kotlinx.serialization.json.Json

internal class DataStoreStorageFactory(
    private val context: Context,
) : StorageFactory{

    override fun <T : Any> createStorage(
        key: String,
        clazz: Class<T>,
        defaultValue: T?
    ): Storage<T> {
        val dataStoreSerializer = JsonDataStoreSerializer(
            typeOfT = clazz,
            stringFormat = stringFormat,
            defaultValue = defaultValue
        )

        return DataStoreStorage(context, dataStoreSerializer, key, defaultValue)
    }

    companion object {
        private val stringFormat: StringFormat = Json {
            ignoreUnknownKeys = true
        }
    }
}