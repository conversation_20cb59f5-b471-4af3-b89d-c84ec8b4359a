package dev.paypass.system.storage.impl

import android.content.Context
import dev.paypass.system.storage.StorageFactory
import dev.paypass.system.storage.StorageFactoryProvider
import dev.paypass.system.storage.StorageType

internal class StorageFactoryProviderImpl(
    private val context: Context
) : StorageFactoryProvider {

    override fun getStorageFactory(storageType: StorageType): StorageFactory {
        return when (storageType) {
            StorageType.PERSISTENT -> DataStoreStorageFactory(context)
            StorageType.IN_MEMORY -> InMemoryStorageFactory()
        }
    }
}