package dev.paypass.system.device

import kotlinx.coroutines.flow.StateFlow

interface DeviceManager {

    val isIgnoringBatteryOptimizations: StateFlow<Boolean>
    val isWakeLockEnabled: StateFlow<Boolean>

    fun requestIgnoreBatteryOptimization()

    fun acquireWakeLock()

    fun keepScreenOn(enabled: Boolean)

    fun getBatteryLevel(): Int

    fun getBatteryState(): BatteryState

    fun getThermalLevel(): Int

    enum class BatteryState {
        UNKNOWN,
        CHARGING,
        DISCHARGING,
        NOT_CHARGING,
        FULL;

        companion object {
            fun fromAndroidState(state: Int): BatteryState = when (state) {
                android.os.BatteryManager.BATTERY_STATUS_CHARGING -> CHARGING
                android.os.BatteryManager.BATTERY_STATUS_DISCHARGING -> DISCHARGING
                android.os.BatteryManager.BATTERY_STATUS_NOT_CHARGING -> NOT_CHARGING
                android.os.BatteryManager.BATTERY_STATUS_FULL -> FULL
                else -> UNKNOWN
            }
        }
    }
}