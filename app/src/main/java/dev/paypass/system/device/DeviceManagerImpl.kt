package dev.paypass.system.device

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.BatteryManager
import android.os.Build
import android.os.PowerManager
import android.provider.Settings
import android.view.WindowManager
import androidx.core.content.ContextCompat
import androidx.core.content.getSystemService
import androidx.core.net.toUri
import com.arkivanov.essenty.lifecycle.doOnResume
import dev.paypass.system.activity.CurrentActivityProvider
import dev.paypass.system.device.DeviceManager.BatteryState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import timber.log.Timber

internal class DeviceManagerImpl(
    private val currentActivityProvider: CurrentActivityProvider,
    private val context: Context
) : DeviceManager {

    private val powerManager = context.getSystemService<PowerManager>() ?: error("PowerManager is null")

    private var wakeLock: PowerManager.WakeLock? = null

    private val mutableIsIgnoringBatteryOptimizations = MutableStateFlow(false)
    override val isIgnoringBatteryOptimizations =
        mutableIsIgnoringBatteryOptimizations.asStateFlow()

    private val mutableIsWakeLockEnabled = MutableStateFlow(false)
    override val isWakeLockEnabled = mutableIsWakeLockEnabled.asStateFlow()

    init {
        currentActivityProvider.lifecycle.doOnResume {
            mutableIsIgnoringBatteryOptimizations.value = powerManager.isIgnoringBatteryOptimizations(context.packageName)
        }
    }

    @SuppressLint("BatteryLife")
    override fun requestIgnoreBatteryOptimization() {
        val intent = Intent(
            Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS,
            "package:${context.packageName}".toUri()
        )

        currentActivityProvider.currentActivity?.startActivity(intent)
    }

    @SuppressLint("WakelockTimeout")
    override fun acquireWakeLock() {
        wakeLock = powerManager.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, WAKE_LOCK_TAG)

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            val mainExecutor = ContextCompat.getMainExecutor(context)
            wakeLock?.setStateListener(mainExecutor) { enabled ->
                mutableIsWakeLockEnabled.value = enabled
            }
        }

        wakeLock?.acquire()
    }

    override fun keepScreenOn(enabled: Boolean) {
        val currentActivity = currentActivityProvider.currentActivity ?: error("No current activity")

        if (enabled) {
            currentActivity.window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        } else {
            currentActivity.window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        }

        Timber.d("keepScreenOn: $enabled")
    }

    override fun getBatteryLevel(): Int {
        val batteryIntent = context.registerReceiver(
            null,
            IntentFilter(Intent.ACTION_BATTERY_CHANGED)
        ) ?: return -1

        val level = batteryIntent.getIntExtra(BatteryManager.EXTRA_LEVEL, -1)
        val scale = batteryIntent.getIntExtra(BatteryManager.EXTRA_SCALE, -1)

        return if (level != -1 && scale != -1) {
            (level * 100) / scale
        } else -1
    }

    override fun getBatteryState(): BatteryState {
        val batteryIntent = context.registerReceiver(
            null,
            IntentFilter(Intent.ACTION_BATTERY_CHANGED)
        ) ?: return BatteryState.UNKNOWN

        val status = batteryIntent.getIntExtra(
            BatteryManager.EXTRA_STATUS,
            BatteryManager.BATTERY_STATUS_UNKNOWN
        )

        return BatteryState.fromAndroidState(status)
    }

    override fun getThermalLevel(): Int {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            return powerManager.currentThermalStatus
        }
        return PowerManager.THERMAL_STATUS_NONE
    }

    companion object {
        private const val WAKE_LOCK_TAG = "PAYPASS:WAKE_LOCK"
    }
}