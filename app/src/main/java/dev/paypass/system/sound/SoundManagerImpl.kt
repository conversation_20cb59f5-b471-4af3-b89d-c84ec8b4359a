package dev.paypass.system.sound

import android.content.Context
import android.media.MediaPlayer
import dev.paypass.R

internal class SoundManagerImpl(
    context: Context
) : SoundManager {

    private val mediaPlayer = MediaPlayer.create(context, R.raw.alert)

    override fun playAlertSound(isLooping: <PERSON>olean) {
        mediaPlayer.isLooping = isLooping
        mediaPlayer.start()
    }

    override fun stopAlertSound() {
        mediaPlayer.stop()
    }
}