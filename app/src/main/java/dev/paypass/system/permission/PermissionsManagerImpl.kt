package dev.paypass.system.permission

import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.provider.Settings
import androidx.activity.result.contract.ActivityResultContracts
import com.judemanutd.autostarter.AutoStartPermissionHelper
import dev.paypass.system.activity.CurrentActivityProvider
import kotlinx.coroutines.suspendCancellableCoroutine
import java.util.concurrent.atomic.AtomicInteger
import kotlin.coroutines.resume

class PermissionsManagerImpl(
    private val currentActivityProvider: CurrentActivityProvider
) : PermissionsManager {

    private val keyIncrement = AtomicInteger(0)

    private val currentActivity
        get() = currentActivityProvider.currentActivity

    override suspend fun requestPermission(permission: Permission): PermissionStatus {
        val currentActivity = currentActivity ?: return PermissionStatus.Denied(isRationale = false)

        return suspendCancellableCoroutine { continuation ->
            val launcher = currentActivity.activityResultRegistry.register(
                "permission_${keyIncrement.getAndIncrement()}",
                ActivityResultContracts.RequestPermission()
            ) { result ->
                continuation.resume(
                    if (result) {
                        PermissionStatus.Granted
                    } else {
                        val shouldShowRationale = currentActivity.shouldShowRequestPermissionRationale(permission.androidPermission)
                        PermissionStatus.Denied(shouldShowRationale.not())
                    }
                )
            }

            launcher.launch(permission.androidPermission)

            continuation.invokeOnCancellation {
                launcher.unregister()
            }
        }
    }

    override suspend fun requestPermissions(permissions: List<Permission>): Map<Permission, PermissionStatus> {
        val currentActivity = currentActivity ?: return permissions.associateWith {
            PermissionStatus.Denied(isRationale = false)
        }

        return suspendCancellableCoroutine { continuation ->
            val launcher = currentActivity.activityResultRegistry.register(
                "permission_${keyIncrement.getAndIncrement()}",
                ActivityResultContracts.RequestMultiplePermissions()
            ) { result ->
                continuation.resume(permissions.associateWith { permission ->
                    if (result[permission.androidPermission] == true) {
                        PermissionStatus.Granted
                    } else {
                        val shouldShowRationale =
                            currentActivity.shouldShowRequestPermissionRationale(permission.androidPermission)
                        PermissionStatus.Denied(shouldShowRationale)
                    }
                })
            }

            launcher.launch(permissions.map { it.androidPermission }.toTypedArray())

            continuation.invokeOnCancellation {
                launcher.unregister()
            }
        }
    }

    override fun checkPermission(permission: Permission): PermissionStatus {
        val currentActivity = currentActivity ?: return PermissionStatus.Denied(isRationale = false)

        return if (currentActivity.checkSelfPermission(permission.androidPermission) == PackageManager.PERMISSION_GRANTED) {
            PermissionStatus.Granted
        } else {
            val shouldShowRationale = currentActivity.shouldShowRequestPermissionRationale(permission.androidPermission)
            PermissionStatus.Denied(shouldShowRationale)
        }
    }

    override fun checkPermissions(permissions: List<Permission>): Map<Permission, PermissionStatus> {
        return permissions.associateWith { checkPermission(it) }
    }

    override fun openPermissionSettings() {
        val currentActivity = currentActivity ?: return

        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
            data = Uri.fromParts("package", currentActivity.packageName, null)
        }

        currentActivity.startActivity(intent)
    }

    override fun isOemAutoStartPermissionAvailable(): Boolean {
        val currentActivity = currentActivity ?: return false
        return AutoStartPermissionHelper.getInstance().isAutoStartPermissionAvailable(currentActivity, onlyIfSupported = true)
    }

    override fun openOemAutoStartPermissionSettings() {
        val currentActivity = currentActivity ?: return

        AutoStartPermissionHelper.getInstance().getAutoStartPermission(currentActivity)
    }
}