package dev.paypass.system.permission

interface PermissionsManager {

    suspend fun requestPermission(permission: Permission): PermissionStatus

    suspend fun requestPermissions(permissions: List<Permission>): Map<Permission, PermissionStatus>

    fun checkPermission(permission: Permission): PermissionStatus

    fun checkPermissions(permissions: List<Permission>): Map<Permission, PermissionStatus>

    fun openPermissionSettings()

    fun isOemAutoStartPermissionAvailable(): Boolean

    fun openOemAutoStartPermissionSettings()
}