package dev.paypass.system.permission

import android.Manifest
import android.os.Build
import androidx.annotation.RequiresApi

enum class Permission(
    internal val androidPermission: String,
) {
    READ_SMS(Manifest.permission.READ_SMS),
    RECEIVE_SMS(Manifest.permission.RECEIVE_SMS),
    CAMERA(Manifest.permission.CAMERA),
    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    POST_NOTIFICATIONS(Manifest.permission.POST_NOTIFICATIONS),
    ACCESS_FINE_LOCATION(Manifest.permission.ACCESS_FINE_LOCATION),
    @RequiresApi(Build.VERSION_CODES.Q)
    ACCESS_BACKGROUND_LOCATION(Manifest.permission.ACCESS_BACKGROUND_LOCATION);
}