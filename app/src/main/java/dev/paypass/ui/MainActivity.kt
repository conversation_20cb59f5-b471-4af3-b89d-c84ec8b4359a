package dev.paypass.ui

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.SystemBarStyle
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.ui.Modifier
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import coil3.ImageLoader
import coil3.compose.setSingletonImageLoaderFactory
import coil3.request.crossfade
import com.arkivanov.decompose.defaultComponentContext
import dev.paypass.ui.component.RootComponent
import dev.paypass.ui.component.RootUI
import dev.paypass.ui.theme.PayPassTheme
import org.koin.android.ext.android.inject

class MainActivity : ComponentActivity() {

    private val componentFactory by inject<ComponentFactory>()

    override fun onCreate(savedInstanceState: Bundle?) {
        val splashScreen = installSplashScreen()
        super.onCreate(savedInstanceState)

        enableEdgeToEdge(
            statusBarStyle = SystemBarStyle.auto(
                lightScrim = android.graphics.Color.TRANSPARENT,
                darkScrim = android.graphics.Color.TRANSPARENT
            )
        )

        val rootComponent: RootComponent = componentFactory.createComponent(
            context = defaultComponentContext()
        )

        setContent {
            setSingletonImageLoaderFactory { context ->
                ImageLoader.Builder(context)
                    .crossfade(true)
                    .build()
            }

            PayPassTheme(darkTheme = true) {
                RootUI(
                    modifier = Modifier.fillMaxSize(),
                    component = rootComponent
                )
            }
        }
    }
}