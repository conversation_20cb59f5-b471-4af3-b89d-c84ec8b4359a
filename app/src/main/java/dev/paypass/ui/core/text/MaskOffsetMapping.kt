package dev.paypass.ui.core.text

import androidx.compose.ui.text.input.OffsetMapping

internal class MaskOffsetMapping(
    private val mask: String,
    private val originalTextLength: Int,
    private val prefixLength: Int = 0,
    private val isPlaceholder: Char.() -> Boolean
) : OffsetMapping {
    override fun originalToTransformed(offset: Int): Int {
        // original says "123456" with mask "xxx-xxx" -> transformed says "123-456"

        var transformedOffset = offset + prefixLength
        var placeholderCount = 0

        for (maskChar in mask) {
            if (placeholderCount == offset) {
                break
            } else if (maskChar.isPlaceholder()) {
                placeholderCount++
            } else {
                transformedOffset++
            }
        }

        val result = transformedOffset.coerceAtMost(prefixLength + mask.length)

        return result
    }

    override fun transformedToOriginal(offset: Int): Int {
        // transformed says "123-456" with mask "xxx-xxx" -> original says "123456". mapping: 7 -> 6

        var originalOffset = (offset - prefixLength).coerceAtLeast(0)
        var placeholderCount = 0

        for (maskChar in mask) {
            if (placeholderCount == offset) {
                break
            } else if (maskChar.isPlaceholder()) {
                placeholderCount++
            } else {
                originalOffset--
            }
        }

        val result =  originalOffset.coerceIn(minimumValue = 0, maximumValue = originalTextLength)

        return result
    }
}