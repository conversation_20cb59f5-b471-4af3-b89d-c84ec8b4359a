package dev.paypass.ui.component.selfupdate

import androidx.compose.runtime.State
import androidx.compose.runtime.asFloatState
import androidx.compose.runtime.mutableFloatStateOf

data class SelfUpdateUiState(
    val status: Status,
) {

    private val mutableProgress = mutableFloatStateOf(-1F)

    val progress: State<Float> = mutableProgress.asFloatState()

    fun setProgress(progress: Float) {
        mutableProgress.floatValue = progress
    }

    enum class Status {
        Checking,
        LastVersionInstalled,
        NewVersionAvailable,
        Downloading,
        ReadyForInstall
    }

    companion object {

        val Default = SelfUpdateUiState(
            status = Status.Checking,
        )
    }
}