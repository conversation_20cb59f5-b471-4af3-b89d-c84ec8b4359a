package dev.paypass.ui.component.main.widget

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import dev.paypass.ui.theme.PayPassTheme
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf

@Composable
internal fun MainDebugInfoBlock(
    modifier: Modifier = Modifier,
    urlForPushValue: String?,
    urlForSmsValue: String?,
    urlForHeartBeatValue: String?,
    worksInQueueCount: Int,
    worksInErrorCount: Int,
    isKeepScreenOn: Boolean,
    onCheckNotificationListeningClicked: () -> Unit,
    onKeepScreenOnCheckedChanged: (Boolean) -> Unit,
    isOemOptimisationSettingsButtonVisible: Boolean,
    onOemOptimisationSettingsClicked: () -> Unit,
    onNotificationListenerSettingsClicked: () -> Unit,
    onRestartServiceClicked: () -> Unit,
    onRestartAppClicked: () -> Unit,
) {
    Column(
        modifier = modifier,
    ) {
        Text(
            text = "Debug Info",
            maxLines = 1,
            style = MaterialTheme.typography.labelLarge
        )

        MainDebugInfoColumnItem(
            items = persistentListOf(
                ColumnItemState(
                    title = "Push",
                    value = urlForPushValue ?: "N/N",
                    color = if (urlForPushValue != null) MaterialTheme.colorScheme.onSurface else Color.Red
                ),
                ColumnItemState(
                    title = "SMS",
                    value = urlForSmsValue ?: "N/N",
                    color = if (urlForSmsValue != null) MaterialTheme.colorScheme.onSurface else Color.Red
                ),
                ColumnItemState(
                    title = "HeartBeat",
                    value = urlForHeartBeatValue ?: "N/N",
                    color = if (urlForHeartBeatValue != null) MaterialTheme.colorScheme.onSurface else Color.Red
                ),
                ColumnItemState(
                    title = "Задачи в очереди",
                    value = worksInQueueCount.toString(),
                    color = Color.Yellow
                ),
                ColumnItemState(
                    title = "Задачи с ошибкой",
                    value = worksInErrorCount.toString(),
                    color = Color.Red
                )
            )
        )

        SwitchItem(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 16.dp),
            title = "Держать экран включённым",
            isChecked = isKeepScreenOn,
            onCheckedChange = onKeepScreenOnCheckedChanged
        )

        Button(
            modifier = Modifier.fillMaxWidth(),
            colors = ButtonDefaults.buttonColors(
                containerColor = MaterialTheme.colorScheme.tertiary,
                contentColor = MaterialTheme.colorScheme.onPrimary
            ),
            onClick = onCheckNotificationListeningClicked
        ) {
            Text(text = "Проверить службу уведомлений")
        }

        Button(
            modifier = Modifier.fillMaxWidth(),
            colors = ButtonDefaults.buttonColors(
                containerColor = MaterialTheme.colorScheme.primary,
                contentColor = MaterialTheme.colorScheme.onPrimary
            ),
            onClick = onNotificationListenerSettingsClicked,
        ) {
            Text(text = "Настройки службы уведомлений")
        }

        if (isOemOptimisationSettingsButtonVisible) {
            Button(
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    contentColor = MaterialTheme.colorScheme.onPrimary
                ),
                onClick = onOemOptimisationSettingsClicked
            ) {
                Text(text = "Настройки оптимизации")
            }
        }

        Button(
            modifier = Modifier.fillMaxWidth(),
            onClick = onRestartServiceClicked,
            colors = ButtonDefaults.buttonColors(
                containerColor = MaterialTheme.colorScheme.error,
                contentColor = MaterialTheme.colorScheme.onError
            )
        ) {
            Text(text = "Перезапустить службу уведомлений")
        }

        Button(
            modifier = Modifier.fillMaxWidth(),
            onClick = onRestartAppClicked,
            colors = ButtonDefaults.buttonColors(
                containerColor = MaterialTheme.colorScheme.error,
                contentColor = MaterialTheme.colorScheme.onError
            )
        ) {
            Text(text = "Перезапустить приложение")
        }
    }
}

private data class ColumnItemState(
    val title: String,
    val value: String,
    val color: Color
)

@Composable
private fun MainDebugInfoColumnItem(
    modifier: Modifier = Modifier,
    items: ImmutableList<ColumnItemState>
) {
    Box(
        modifier = modifier
            .defaultMinSize(minHeight = 60.dp)
            .padding(vertical = 16.dp),
        contentAlignment = Alignment.Center
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(4.dp),
        ) {
            items.forEach { (title, value, color) ->
                Row(
                    horizontalArrangement = Arrangement.spacedBy(4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        modifier = Modifier.wrapContentHeight(),
                        text = "$title: $value",
                        maxLines = 1,
                        style = MaterialTheme.typography.bodyMedium,
                        color = color
                    )
                }
            }
        }
    }
}

@Composable
private fun SwitchItem(
    modifier: Modifier = Modifier,
    title: String,
    isChecked: Boolean,
    onCheckedChange: (Boolean) -> Unit
) {
    Row(
        modifier = modifier,
    ) {
        Text(
            modifier = Modifier
                .weight(1f)
                .align(Alignment.CenterVertically),
            text = title,
            textAlign = TextAlign.Start,
        )

        Switch(
            checked = isChecked,
            onCheckedChange = onCheckedChange
        )
    }
}

@Preview
@Composable
private fun MainDebugInfoBlockPreview() = PayPassTheme {
    MainDebugInfoBlock(
        modifier = Modifier.fillMaxWidth(),
        urlForPushValue = "https://push",
        urlForSmsValue = "https://sms",
        urlForHeartBeatValue = "https://heartbeat",
        worksInQueueCount = 0,
        worksInErrorCount = 0,
        isKeepScreenOn = true,
        onCheckNotificationListeningClicked = {},
        onKeepScreenOnCheckedChanged = {},
        isOemOptimisationSettingsButtonVisible = true,
        onOemOptimisationSettingsClicked = {},
        onNotificationListenerSettingsClicked = {},
        onRestartServiceClicked = {},
        onRestartAppClicked = {},
    )
}