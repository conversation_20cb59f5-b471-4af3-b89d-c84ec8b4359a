package dev.paypass.ui.component.main

import androidx.compose.runtime.Immutable

@Immutable
data class MainScreenState(
    val userId: String? = null,
    val urlForPush: String? = null,
    val urlForSms: String? = null,
    val urlForHeartbeat: String? = null,
    val worksInQueueCount: Int = 0,
    val worksInErrorCount: Int = 0,
    val notificationListeningStatus: NotificationListeningStatus = NotificationListeningStatus.NOT_LISTENING,
    val isKeepScreenOn: Boolean = false,
    val isOemOptimisationSettingsButtonVisible: Boolean = false,
    val appVersion: String = "",
) {

    enum class NotificationListeningStatus {
        CHECKING, LISTENING, NOT_LISTENING
    }
}