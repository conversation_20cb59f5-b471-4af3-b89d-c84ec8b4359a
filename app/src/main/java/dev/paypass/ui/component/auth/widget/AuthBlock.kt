package dev.paypass.ui.component.auth.widget

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp

@Composable
internal fun AuthBlock(
    modifier: Modifier = Modifier,
    phoneFieldText: String?,
    phoneFieldValueChanged: (String) -> Unit,
    onScanQrCodeClicked: () -> Unit,
    isConfigReceived: Boolean,
    isLoginButtonEnabled: Boolean,
    onLoginButtonClicked: () -> Unit,
    isLoading: Boolean,
) {
    Card(
        modifier = modifier,
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            SettingsPhoneItem(
                modifier = Modifier.fillMaxWidth(),
                fieldText = phoneFieldText,
                fieldValueChanged = phoneFieldValueChanged,
            )

            HorizontalDivider()

            QrScannerSection(
                modifier = Modifier.fillMaxWidth(),
                onScanQrCodeClicked = onScanQrCodeClicked,
                isConfigReceived = isConfigReceived,
            )

            Button(
                modifier = Modifier.fillMaxWidth(),
                onClick = onLoginButtonClicked,
                enabled = isLoginButtonEnabled && isLoading.not(),
            ) {
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.then(Modifier.size(32.dp))
                    )
                } else {
                    Text(
                        text = "Вход",
                        style = MaterialTheme.typography.bodyLarge,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
        }
    }
}

@Composable
private fun SettingsPhoneItem(
    modifier: Modifier = Modifier,
    fieldText: String?,
    fieldValueChanged: (String) -> Unit
) {
    OutlinedTextField(
        modifier = modifier,
        value = fieldText ?: "",
        label = { Text(text = "ID (номер телефона)") },
        onValueChange = fieldValueChanged,
        keyboardOptions = KeyboardOptions(
            keyboardType = KeyboardType.Phone
        ),
        singleLine = true
    )
}

@Composable
private fun QrScannerSection(
    modifier: Modifier = Modifier,
    onScanQrCodeClicked: () -> Unit,
    isConfigReceived: Boolean,
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.weight(1F)
        ) {
            Text(
                text = "Конфигурация",
                style = MaterialTheme.typography.bodyMedium
            )
            Text(
                text = if (isConfigReceived) "✓ Получена" else "Не получена",
                style = MaterialTheme.typography.bodySmall,
                color = if (isConfigReceived)
                    MaterialTheme.colorScheme.primary
                else
                    MaterialTheme.colorScheme.error
            )
        }

        Button(
            onClick = onScanQrCodeClicked,
        ) {
            Text(
                text = "Сканировать QR",
                maxLines = 1,
            )
        }
    }
}