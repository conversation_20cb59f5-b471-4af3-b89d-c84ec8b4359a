package dev.paypass.ui.component

import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.router.slot.ChildSlot
import com.arkivanov.decompose.router.slot.SlotNavigation
import com.arkivanov.decompose.router.slot.activate
import com.arkivanov.decompose.router.slot.childSlot
import com.arkivanov.decompose.router.slot.dismiss
import com.arkivanov.decompose.router.stack.ChildStack
import com.arkivanov.decompose.router.stack.StackNavigation
import com.arkivanov.decompose.router.stack.childStack
import com.arkivanov.decompose.router.stack.replaceAll
import com.arkivanov.essenty.lifecycle.doOnResume
import dev.paypass.domain.auth.use_case.IsAuthChangesUseCase
import dev.paypass.domain.condition.GetAllAppConditionsUseCase
import dev.paypass.domain.config.app.use_case.AppConfigChangesUseCase
import dev.paypass.system.device.DeviceManager
import dev.paypass.ui.ComponentFactory
import dev.paypass.ui.component.RootComponent.ScreenChild
import dev.paypass.ui.component.RootComponent.DialogChild
import dev.paypass.ui.component.app_conditions.AppConditionsScreenComponent
import dev.paypass.ui.component.main.MainScreenComponent
import dev.paypass.ui.createComponent
import dev.paypass.ui.util.componentScope
import dev.paypass.ui.util.toStateFlow
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable
import timber.log.Timber

class RootComponentImpl(
    context: ComponentContext,
    private val componentFactory: ComponentFactory,
    getAllAppConditionsUseCase: GetAllAppConditionsUseCase,
    isAuthChangesUseCase: IsAuthChangesUseCase,
    private val appConfigChangesUseCase: AppConfigChangesUseCase,
    private val deviceManager: DeviceManager
) : RootComponent,
    ComponentContext by context {

    private val screenNavigation = StackNavigation<ScreenConfiguration>()
    private val dialogNavigation = SlotNavigation<DialogConfiguration>()

    override val screenChildStack: StateFlow<ChildStack<*, ScreenChild>> = childStack(
        source = screenNavigation,
        serializer = ScreenConfiguration.serializer(),
        initialConfiguration = ScreenConfiguration.Splash,
        childFactory = ::screenChildFactory,
    ).toStateFlow(lifecycle)

    override val dialogChildSlot: StateFlow<ChildSlot<*, DialogChild>> = childSlot(
        source = dialogNavigation,
        serializer = DialogConfiguration.serializer(),
        childFactory = ::dialogChildFactory
    ).toStateFlow(lifecycle)

    init {
        componentScope.launch {
            combine(
                getAllAppConditionsUseCase(),
                isAuthChangesUseCase(),
                ::Pair
            ).collectLatest { (appConditions, isAuth) ->
                if (isAuth) {
                    val isAllConditionsSatisfied = appConditions.values.all { it }

                    if (isAllConditionsSatisfied) {
                        screenNavigation.replaceAll(ScreenConfiguration.Settings)
                    } else {
                        screenNavigation.replaceAll(ScreenConfiguration.AppConditions)
                    }
                } else {
                    screenNavigation.replaceAll(ScreenConfiguration.Auth)
                }
            }
        }

        keepScreenOnByConfig()
    }

    override fun onDialogDismissRequested(child: DialogChild) {
        dialogNavigation.dismiss()
    }

    private fun screenChildFactory(
        screenConfiguration: ScreenConfiguration,
        context: ComponentContext
    ): ScreenChild {
        return when (screenConfiguration) {
            ScreenConfiguration.Splash -> ScreenChild.Splash

            ScreenConfiguration.AppConditions -> ScreenChild.AppConditions(
                component = componentFactory.createComponent(
                    context = context,
                    params = AppConditionsComponentCallbacks()
                )
            )

            ScreenConfiguration.Auth -> ScreenChild.Auth(
                component = componentFactory.createComponent(context)
            )

            ScreenConfiguration.Settings -> ScreenChild.Settings(
                component = componentFactory.createComponent(
                    context = context,
                    params = MainScreenComponentRouter()
                )
            )
        }
    }

    private fun dialogChildFactory(
        dialogConfiguration: DialogConfiguration,
        context: ComponentContext
    ): DialogChild {
        return when (dialogConfiguration) {
            DialogConfiguration.Debug -> DialogChild.Debug(
                component = componentFactory.createComponent(context)
            )
        }
    }

    private fun keepScreenOnByConfig() {
        appConfigChangesUseCase()
            .map { it.keepScreenOn }
            .distinctUntilChanged()
            .onEach { keepScreenOn ->
                try {
                    lifecycle.doOnResume(isOneTime = true) {
                        deviceManager.keepScreenOn(keepScreenOn)
                    }
                } catch (e: Exception) {
                    Timber.e(e, "Failed tum keep screen on $keepScreenOn")
                }
            }
            .launchIn(componentScope)
    }

    @Serializable
    private sealed interface ScreenConfiguration {
        @Serializable
        data object Splash : ScreenConfiguration

        @Serializable
        data object AppConditions : ScreenConfiguration

        @Serializable
        data object Auth : ScreenConfiguration

        @Serializable
        data object Settings : ScreenConfiguration
    }

    @Serializable
    private sealed interface DialogConfiguration {
        @Serializable
        data object Debug : DialogConfiguration
    }

    private inner class AppConditionsComponentCallbacks : AppConditionsScreenComponent.Callbacks {

        override fun onAllConditionsSatisfied() {
            componentScope.launch {
                delay(OPEN_SETTINGS_AFTER_ALL_CONDITIONS_SATISFIED_DELAY_MS)
                screenNavigation.replaceAll(ScreenConfiguration.Settings)
            }
        }
    }

    private inner class MainScreenComponentRouter : MainScreenComponent.Router {

        override fun showDebugDialog() {
            dialogNavigation.activate(DialogConfiguration.Debug)
        }
    }

    companion object {
        private const val OPEN_SETTINGS_AFTER_ALL_CONDITIONS_SATISFIED_DELAY_MS = 1_000L
    }
}