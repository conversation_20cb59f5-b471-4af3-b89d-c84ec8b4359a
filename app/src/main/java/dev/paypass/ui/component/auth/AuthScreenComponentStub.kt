package dev.paypass.ui.component.auth

import dev.paypass.ui.component.auth.state.AuthScreenState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update

internal class AuthScreenComponentStub(
    defaultState: AuthScreenState = AuthScreenState.Default
) : AuthScreenComponent {

    private val mutableState = MutableStateFlow(defaultState)
    override val state = mutableState.asStateFlow()

    override fun onIdFieldChanged(idFieldValue: String) {
        mutableState.update { state ->
            state.copy(
                idFieldText = idFieldValue
            )
        }
    }

    override fun onScanQrClicked() {
        mutableState.update { state ->
            state.copy(
                cameraPermissionDialog = AuthScreenState.CameraPermissionDialog.DENIED
            )
        }
    }

    override fun onLoginClicked() {
        // no-op
    }

    override fun onCameraPermissionDialogOpenSettingsClicked() {
        mutableState.update { state ->
            state.copy(cameraPermissionDialog = null)
        }
    }

    override fun onCameraPermissionDialogDismissRequest() {
        mutableState.update { state ->
            state.copy(cameraPermissionDialog = null)
        }
    }
}