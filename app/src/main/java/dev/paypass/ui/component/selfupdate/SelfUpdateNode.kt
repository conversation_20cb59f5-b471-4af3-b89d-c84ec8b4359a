package dev.paypass.ui.component.selfupdate

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import dev.paypass.ui.theme.PayPassTheme

@Composable
internal fun SelfUpdateNode(
    modifier: Modifier = Modifier,
    status: SelfUpdateUiState.Status,
    progress: Float,
    onStartDownloadClicked: () -> Unit,
    onInstallClicked: () -> Unit
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        when (status) {
            SelfUpdateUiState.Status.Checking -> {
                Text(
                    modifier = Modifier.weight(1F),
                    text = "Проверка обновлений..."
                )
                CircularProgressIndicator(
                    modifier = Modifier.padding(start = 8.dp)
                )
            }

            SelfUpdateUiState.Status.LastVersionInstalled -> {
                Text(
                    modifier = Modifier.weight(1F),
                    text = "Последняя версия установлена"
                )
            }

            SelfUpdateUiState.Status.NewVersionAvailable -> {
                Text(
                    modifier = Modifier.weight(1F),
                    text = "Доступна новая версия"
                )
                Button(
                    modifier = Modifier.padding(start = 8.dp),
                    onClick = onStartDownloadClicked
                ) {
                    Text(text = "Обновить")
                }
            }

            SelfUpdateUiState.Status.Downloading -> {
                Text(
                    modifier = Modifier.weight(1F),
                    text = "Загрузка обновления..."
                )
                CircularProgressIndicator(
                    modifier = Modifier.padding(start = 8.dp),
                    progress = progress
                )
            }

            SelfUpdateUiState.Status.ReadyForInstall -> {
                Text(
                    modifier = Modifier.weight(1F),
                    text = "Обновление готово к установке"
                )
                Button(
                    modifier = Modifier.padding(start = 8.dp),
                    onClick = onInstallClicked
                ) {
                    Text(text = "Установить")
                }
            }
        }
    }
}

@Preview
@Composable
private fun MainSelfUpdateBlockPreview() {
    PayPassTheme {
        SelfUpdateNode(
            status = SelfUpdateUiState.Status.NewVersionAvailable,
            progress = 0f,
            onStartDownloadClicked = {},
            onInstallClicked = {}
        )
    }
}