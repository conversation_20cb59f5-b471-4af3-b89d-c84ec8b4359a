package dev.paypass.ui.component.auth.state

import androidx.compose.runtime.Stable

@Stable
data class AuthScreenState(
    val idFieldText: String,
    val isValid: <PERSON>olean,
    val isLoading: <PERSON><PERSON>an,
    val cameraPermissionDialog: CameraPermissionDialog?,
    val isConfigReceived: <PERSON><PERSON><PERSON>,
) {

    enum class CameraPermissionDialog {
        DENIED,
        DENIED_FOREVER
    }

    companion object {
        val Default = AuthScreenState(
            idFieldText = "",
            isValid = false,
            isLoading = false,
            cameraPermissionDialog = null,
            isConfigReceived = false
        )
    }
}