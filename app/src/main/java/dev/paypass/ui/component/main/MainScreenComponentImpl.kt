package dev.paypass.ui.component.main

import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.childContext
import com.arkivanov.essenty.lifecycle.doOnResume
import dev.paypass.BuildConfig
import dev.paypass.domain.auth.AuthRepository
import dev.paypass.domain.config.app.use_case.AppConfigChangesUseCase
import dev.paypass.domain.config.app.use_case.AppConfigUpdateUseCase
import dev.paypass.domain.config.remote.use_case.RemoteConfigChangesUseCase
import dev.paypass.domain.work.WorkInfo
import dev.paypass.domain.work.WorkInfoManager
import dev.paypass.system.app.ApplicationManager
import dev.paypass.system.notification.NotificationListenerChecker
import dev.paypass.system.notification.SystemNotificationsManager
import dev.paypass.system.permission.PermissionsManager
import dev.paypass.ui.ComponentFactory
import dev.paypass.ui.component.selfupdate.SelfUpdateComponent
import dev.paypass.ui.createComponent
import dev.paypass.ui.util.componentScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

internal class MainScreenComponentImpl(
    context: ComponentContext,
    componentFactory: ComponentFactory,
    private val router: MainScreenComponent.Router,
    private val remoteConfigChangesUseCase: RemoteConfigChangesUseCase,
    private val appConfigChangesUseCase: AppConfigChangesUseCase,
    private val appConfigUpdateUseCase: AppConfigUpdateUseCase,
    private val authRepository: AuthRepository,
    private val workInfoManager: WorkInfoManager,
    private val applicationManager: ApplicationManager,
    private val systemNotificationsManager: SystemNotificationsManager,
    private val permissionsManager: PermissionsManager,
    private val notificationListenerChecker: NotificationListenerChecker
) : MainScreenComponent,
    ComponentContext by context {

    override val selfUpdateComponent: SelfUpdateComponent = componentFactory.createComponent(
        context = childContext(key = "selfUpdate")
    )

    private val mutableState = MutableStateFlow(
        MainScreenState(appVersion = "${BuildConfig.VERSION_NAME} (${BuildConfig.VERSION_CODE})")
    )
    override val state = mutableState.asStateFlow()

    init {
        componentScope.launch { subscribeAppConfig() }
        componentScope.launch { subscribeRemoteConfig() }
        componentScope.launch { subscribeWorkInfo() }
        componentScope.launch { subscribeNotificationListeningStatus() }

        componentScope.launch {
            authRepository.authInfoChanges().collectLatest { authInfo ->
                mutableState.update { state ->
                    state.copy(userId = authInfo?.id)
                }
            }
        }

        lifecycle.doOnResume {
            mutableState.update { state ->
                state.copy(
                    isOemOptimisationSettingsButtonVisible = permissionsManager.isOemAutoStartPermissionAvailable()
                )
            }

            componentScope.launch { notificationListenerChecker.check() }
        }
    }

    override fun onCheckNotificationListeningClicked() {
        componentScope.launch { notificationListenerChecker.check() }
    }

    override fun onKeepScreenOnCheckedChanged(isChecked: Boolean) {
        componentScope.launch {
            appConfigUpdateUseCase { appConfig ->
                appConfig.copy(keepScreenOn = isChecked)
            }
        }
    }

    override fun onOemOptimisationSettingsClicked() {
        permissionsManager.openOemAutoStartPermissionSettings()
    }

    override fun onNotificationListenerSettingsClicked() {
        systemNotificationsManager.openNotificationListenerSettings()
    }

    override fun onRestartServiceClicked() {
        systemNotificationsManager.tryReconnect()
    }

    override fun onRestartAppClicked() {
        applicationManager.restartApp()
    }

    override fun onAlertClicked() {
        // no-op
    }

    override fun onAppVersionLongClicked() {
        router.showDebugDialog()
    }

    private suspend fun subscribeRemoteConfig() {
        remoteConfigChangesUseCase().collect { remoteConfig ->
            mutableState.update { state ->
                state.copy(
                    urlForPush = remoteConfig.urlForPush,
                    urlForSms = remoteConfig.urlForSms,
                    urlForHeartbeat = remoteConfig.urlForHeartbeat
                )
            }
        }
    }

    private suspend fun subscribeAppConfig() {
        appConfigChangesUseCase().collect { appConfig ->
            mutableState.update { state ->
                state.copy(
                    isKeepScreenOn = appConfig.keepScreenOn
                )
            }
        }
    }

    private suspend fun subscribeWorkInfo() {
        workInfoManager.allWorksChanges().collect { works ->
            val worksInQueueCount = works.count {
                it.status == WorkInfo.Status.ENQUEUED || it.status == WorkInfo.Status.RUNNING
            }
            val worksInErrorCount = works.count {
                it.status == WorkInfo.Status.FAILED
            }

            mutableState.update { state ->
                state.copy(
                    worksInQueueCount = worksInQueueCount,
                    worksInErrorCount = worksInErrorCount
                )
            }
        }
    }

    private suspend fun subscribeNotificationListeningStatus() {
        notificationListenerChecker.listeningStatus.collectLatest { listeningStatus ->
            mutableState.update { state ->
                state.copy(
                    notificationListeningStatus = when (listeningStatus) {
                        NotificationListenerChecker.ListeningStatus.NOT_LISTENING -> MainScreenState.NotificationListeningStatus.NOT_LISTENING
                        NotificationListenerChecker.ListeningStatus.LISTENING -> MainScreenState.NotificationListeningStatus.LISTENING
                        NotificationListenerChecker.ListeningStatus.CHECKING -> MainScreenState.NotificationListeningStatus.CHECKING
                    }
                )
            }
        }
    }
}