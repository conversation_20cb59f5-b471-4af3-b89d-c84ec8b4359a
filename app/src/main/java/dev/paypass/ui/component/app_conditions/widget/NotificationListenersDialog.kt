package dev.paypass.ui.component.app_conditions.widget

import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.text.style.TextAlign

@Composable
internal fun AppConditionsNotificationListenersDialog(
    onDismissRequest: () -> Unit,
    onOpenSettingsClicked: () -> Unit
) {
    AlertDialog(
        title = {
            Text(
                text = "Необходимо разрешение",
                textAlign = TextAlign.Center
            )
        },
        text = {
            Text(
                text = "Для корректной работы приложения необходимо разрешить доступ к уведомлениям. Пожалуйста, разрешите доступ в настройках уведомлений.",
                textAlign = TextAlign.Center
            )
        },
        onDismissRequest = onDismissRequest,
        confirmButton = {
            TextButton(
                onClick = onOpenSettingsClicked
            ) {
                Text("НАСТРОЙКИ")
            }
        },
    )
}