package dev.paypass.ui.component.auth

import dev.paypass.ui.component.auth.state.AuthScreenState
import kotlinx.coroutines.flow.StateFlow

interface AuthScreenComponent {

    val state: StateFlow<AuthScreenState>

    fun onIdFieldChanged(idFieldValue: String)

    fun onScanQrClicked()

    fun onLoginClicked()

    fun onCameraPermissionDialogOpenSettingsClicked()

    fun onCameraPermissionDialogDismissRequest()
}