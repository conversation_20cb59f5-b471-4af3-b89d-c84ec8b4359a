package dev.paypass.ui.component.selfupdate

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier

@Composable
internal fun SelfUpdateUi(
    modifier: Modifier = Modifier,
    component: SelfUpdateComponent
) {
    val state by component.state.collectAsState()
    val progress by state.progress

    SelfUpdateNode(
        modifier = modifier,
        status = state.status,
        progress = progress,
        onStartDownloadClicked = component::onStartDownloadClicked,
        onInstallClicked = component::onInstallClicked
    )
}