package dev.paypass.ui.component.debug

import com.arkivanov.decompose.ComponentContext
import dev.paypass.domain.config.app.AppConfig
import dev.paypass.domain.config.app.use_case.AppConfigChangesUseCase
import dev.paypass.domain.config.app.use_case.AppConfigUpdateUseCase
import dev.paypass.ui.util.componentScope
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

class AppDebugComponentImpl(
    context: ComponentContext,
    debugConfigChangesUseCase: AppConfigChangesUseCase,
    private val debugConfigUpdateUseCase: AppConfigUpdateUseCase
) : AppDebugComponent,
    ComponentContext by context {

    override val state = debugConfigChangesUseCase()
        .map { it.toUiState() }
        .stateIn(componentScope, SharingStarted.Eagerly, AppDebugUiState.Default)

    override fun onRemoteLogEnabledChanged(isEnabled: Boolean) {
        componentScope.launch {
            debugConfigUpdateUseCase { debugConfig ->
                debugConfig.copy(remoteLogEnabled = isEnabled)
            }
        }
    }

    override fun onCancelSentNotificationsEnableChanged(isEnabled: Boolean) {
        componentScope.launch {
            debugConfigUpdateUseCase { debugConfig ->
                debugConfig.copy(cancelSentNotifications = isEnabled)
            }
        }
    }

    private fun AppConfig.toUiState() = AppDebugUiState(
        isRemoteLogEnabled = remoteLogEnabled,
        isCancelSentNotificationsEnabled = cancelSentNotifications
    )
}