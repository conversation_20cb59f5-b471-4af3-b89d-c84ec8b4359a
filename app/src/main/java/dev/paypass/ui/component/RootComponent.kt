package dev.paypass.ui.component

import com.arkivanov.decompose.router.slot.ChildSlot
import com.arkivanov.decompose.router.stack.ChildStack
import dev.paypass.ui.component.app_conditions.AppConditionsScreenComponent
import dev.paypass.ui.component.auth.AuthScreenComponent
import dev.paypass.ui.component.debug.AppDebugComponent
import dev.paypass.ui.component.main.MainScreenComponent
import kotlinx.coroutines.flow.StateFlow

interface RootComponent {

    val screenChildStack: StateFlow<ChildStack<*, ScreenChild>>

    val dialogChildSlot: StateFlow<ChildSlot<*, DialogChild>>

    fun onDialogDismissRequested(child: DialogChild)

    sealed interface ScreenChild {
        data object Splash : ScreenChild

        data class AppConditions(val component: AppConditionsScreenComponent) : ScreenChild

        data class Auth(val component: AuthScreenComponent) : ScreenChild

        data class Settings(val component: MainScreenComponent) : ScreenChild
    }

    sealed interface DialogChild {
        data class Debug(val component: AppDebugComponent) : DialogChild
    }
}