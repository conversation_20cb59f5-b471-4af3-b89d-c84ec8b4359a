package dev.paypass.ui.component.app_conditions

import dev.paypass.ui.component.app_conditions.state.AppConditionsScreenState
import kotlinx.coroutines.flow.StateFlow

interface AppConditionsScreenComponent {

    val state: StateFlow<AppConditionsScreenState>

    fun onConditionItemClicked(itemId: Int)

    fun onPermissionDialogDismissRequest()

    fun onPermissionDialogOpenSettingsClicked()

    fun onEnableNotificationListenerDialogDismissRequest()

    fun onEnableNotificationListenerDialogOpenSettingsClicked()

    interface Callbacks {
        fun onAllConditionsSatisfied()
    }
}