package dev.paypass.ui.component.auth

import com.arkivanov.decompose.ComponentContext
import dev.paypass.data.auth.error.UserNotFoundException
import dev.paypass.domain.auth.use_case.AuthLoginUseCase
import dev.paypass.domain.auth.use_case.AuthScanQrResult
import dev.paypass.domain.auth.use_case.AuthScanQrUseCase
import dev.paypass.system.permission.PermissionsManager
import dev.paypass.system.toast.ToastManager
import dev.paypass.ui.component.auth.state.AuthScreenState
import dev.paypass.ui.util.componentScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

internal class AuthScreenComponentImpl(
    context: ComponentContext,
    private val permissionsManager: PermissionsManager,
    private val toastManager: ToastManager,
    private val authScanQrUseCase: AuthScanQrUseCase,
    private val authLoginUseCase: AuthLoginUseCase
) : ComponentContext by context,
    AuthScreenComponent {

    private val mutableState = MutableStateFlow(AuthScreenState.Default)
    override val state = mutableState.asStateFlow()

    init {
        componentScope.launch {
            combine(
                state.map { it.idFieldText },
                state.map { it.tokenFieldText },
                transform = { id, token -> id.length in ID_LENGTH_RAGE && token.isNotBlank() }
            ).collect { isValid ->
                mutableState.update { state -> state.copy(isValid = isValid) }
            }
        }
    }

    override fun onIdFieldChanged(idFieldValue: String) {
        mutableState.update { state -> state.copy(idFieldText = formatId(idFieldValue)) }
    }

    override fun onTokenFieldChanged(tokenFieldValue: String) {
        mutableState.update { state -> state.copy(tokenFieldText = tokenFieldValue) }
    }

    override fun onScanQrClicked() {
        componentScope.launch {
            when (val result = authScanQrUseCase()) {
                is AuthScanQrResult.Token -> {
                    mutableState.update { state -> state.copy(tokenFieldText = result.token) }
                }

                is AuthScanQrResult.MissingPermission -> {
                    val cameraPermissionDialog = if (result.isPermissionPermanentlyDenied) {
                        AuthScreenState.CameraPermissionDialog.DENIED_FOREVER
                    } else {
                        AuthScreenState.CameraPermissionDialog.DENIED
                    }

                    mutableState.update { state ->
                        state.copy(cameraPermissionDialog = cameraPermissionDialog)
                    }
                }

                AuthScanQrResult.UserCanceled -> {
                    // no-op
                }

                is AuthScanQrResult.Error -> {
                    toastManager.showToast("Что-то пошло не так")
                }
            }
        }
    }

    override fun onLoginClicked() {
        componentScope.launch {
            val id = state.value.idFieldText
            val token = state.value.tokenFieldText

            mutableState.update { state -> state.copy(isLoading = true) }
            val loginResult = authLoginUseCase(id, token)
            mutableState.update { state -> state.copy(isLoading = false) }

            loginResult.onSuccess {
                toastManager.showToast("Авторизация прошла успешно")
            }.onFailure { exception ->
                if (exception is UserNotFoundException) {
                    toastManager.showToast("Неверный идентификатор или токен")
                } else {
                    toastManager.showToast("Что-то пошло не так")
                }
            }
        }
    }

    override fun onCameraPermissionDialogOpenSettingsClicked() {
        mutableState.update { state ->
            state.copy(cameraPermissionDialog = null)
        }
        permissionsManager.openPermissionSettings()
    }

    override fun onCameraPermissionDialogDismissRequest() {
        mutableState.update { state ->
            state.copy(cameraPermissionDialog = null)
        }
    }

    private fun formatId(id: String): String {
        return id
            .replace(Regex("[^0-9]"), "")
            .take(ID_LENGTH_RAGE.last)
    }

    companion object {
        private val ID_LENGTH_RAGE = 10..12
    }
}