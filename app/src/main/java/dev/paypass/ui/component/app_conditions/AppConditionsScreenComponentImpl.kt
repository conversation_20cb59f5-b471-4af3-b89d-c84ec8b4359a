package dev.paypass.ui.component.app_conditions

import android.os.Build
import com.arkivanov.decompose.ComponentContext
import dev.paypass.domain.condition.AppCondition
import dev.paypass.domain.condition.GetAllAppConditionsUseCase
import dev.paypass.system.device.DeviceManager
import dev.paypass.system.notification.SystemNotificationsManager
import dev.paypass.system.permission.Permission
import dev.paypass.system.permission.PermissionsManager
import dev.paypass.ui.component.app_conditions.state.AppConditionListItemState
import dev.paypass.ui.component.app_conditions.state.AppConditionsScreenState
import dev.paypass.ui.util.componentScope
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

class AppConditionsScreenComponentImpl(
    context: ComponentContext,
    private val callbacks: AppConditionsScreenComponent.Callbacks,
    private val getAllAppConditionsUseCase: GetAllAppConditionsUseCase,
    private val permissionsManager: PermissionsManager,
    private val systemNotificationsManager: SystemNotificationsManager,
    private val deviceManager: DeviceManager
) : AppConditionsScreenComponent, ComponentContext by context {

    private val _state = MutableStateFlow(AppConditionsScreenState())
    override val state = _state.asStateFlow()

    init {
        componentScope.launch {
            getAllAppConditionsUseCase().collectLatest { appConditions ->
                val isAllConditionsSatisfied = appConditions.values.all { it }

                _state.update { state ->
                    state.copy(
                        itemStates = appConditions.entries
                            .sortedBy { (_, isSatisfied) -> isSatisfied }
                            .map { (appCondition, isSatisfied) ->
                                AppConditionListItemState(
                                    id = appCondition.id,
                                    appCondition = appCondition,
                                    isSatisfied = isSatisfied
                                )
                            }
                            .toPersistentList()
                    )
                }

                if (isAllConditionsSatisfied) {
                    callbacks.onAllConditionsSatisfied()
                }
            }
        }
    }

    override fun onConditionItemClicked(itemId: Int) {
        val item = state.value.itemStates.find { it.id == itemId } ?: return
        if (item.isSatisfied) return

        when (item.appCondition) {
            is AppCondition.Permission -> {
                componentScope.launch {
                    val permission = when (item.appCondition) {
                        AppCondition.Permission.READ_SMS -> Permission.READ_SMS
                        AppCondition.Permission.RECEIVE_SMS -> Permission.RECEIVE_SMS
                        AppCondition.Permission.POST_NOTIFICATIONS -> if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                            Permission.POST_NOTIFICATIONS
                        } else {
                            return@launch
                        }
                        AppCondition.Permission.LOCATION -> Permission.ACCESS_FINE_LOCATION
                        AppCondition.Permission.BACKGROUND_LOCATION -> if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                            Permission.ACCESS_BACKGROUND_LOCATION
                        } else {
                            return@launch
                        }
                    }

                    val permissionStatus = permissionsManager.requestPermission(permission)

                    if (permissionStatus.isDeniedRationale) {
                        setShowingPermissionDialog(true)
                    }
                }
            }

            AppCondition.EnableNotificationListener -> {
                setShowingEnableNotificationListenerDialog(true)
            }

            AppCondition.IgnoreBatteryOptimization -> {
                deviceManager.requestIgnoreBatteryOptimization()
            }

            else -> {
                // do nothing
            }
        }
    }

    override fun onPermissionDialogDismissRequest() {
        setShowingPermissionDialog(false)
    }

    override fun onPermissionDialogOpenSettingsClicked() {
        setShowingPermissionDialog(false)

        permissionsManager.openPermissionSettings()
    }

    override fun onEnableNotificationListenerDialogDismissRequest() {
        setShowingEnableNotificationListenerDialog(false)
    }

    override fun onEnableNotificationListenerDialogOpenSettingsClicked() {
        setShowingEnableNotificationListenerDialog(false)

        systemNotificationsManager.openNotificationListenerSettings()
    }

    private fun setShowingPermissionDialog(show: Boolean) {
        _state.update { state -> state.copy(isShowingPermissionDialog = show) }
    }

    private fun setShowingEnableNotificationListenerDialog(show: Boolean) {
        _state.update { state -> state.copy(isShowingEnableNotificationListenerDialog = show) }
    }
}