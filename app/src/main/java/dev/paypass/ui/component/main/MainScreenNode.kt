package dev.paypass.ui.component.main

import android.content.res.Configuration
import androidx.compose.animation.animateColorAsState
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import dev.paypass.R
import dev.paypass.ui.component.main.widget.MainDebugInfoBlock
import dev.paypass.ui.theme.PayPassTheme

@OptIn(ExperimentalFoundationApi::class)
@Composable
internal fun MainScreenNode(
    modifier: Modifier = Modifier,
    userId: String?,
    urlForPushValue: String?,
    urlForSmsValue: String?,
    urlForHeartBeatValue: String?,
    worksInQueueCount: Int,
    worksInErrorCount: Int,
    notificationListeningStatus: MainScreenState.NotificationListeningStatus,
    appVersion: String,
    isKeepScreenOn: Boolean,
    onCheckNotificationListeningClicked: () -> Unit,
    onKeepScreenOnCheckedChanged: (Boolean) -> Unit,
    isOemOptimisationSettingsButtonVisible: Boolean,
    onOemOptimisationSettingsClicked: () -> Unit,
    onNotificationListenerSettingsClicked: () -> Unit,
    onRestartServiceClicked: () -> Unit,
    onRestartAppClicked: () -> Unit,
    onAppVersionLongClicked: () -> Unit,
    onAlertClicked: () -> Unit,
    selfUpdateContent: SelfUpdateContent = SelfUpdateContent.EMPTY
) {
    Column(
        modifier = modifier
            .padding(
                vertical = 16.dp,
                horizontal = 16.dp,
            ),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            Text(
                modifier = Modifier.fillMaxWidth(),
                text = "$userId",
                textAlign = TextAlign.Center,
            )

            NotificationListenerStatus(
                modifier = Modifier.fillMaxWidth(),
                listeningStatus = notificationListeningStatus,
                onAlertClicked = onAlertClicked
            )

            selfUpdateContent.Content(
                modifier = Modifier.fillMaxWidth()
            )

            MainDebugInfoBlock(
                modifier = Modifier.fillMaxWidth(),
                urlForPushValue = urlForPushValue,
                urlForSmsValue = urlForSmsValue,
                urlForHeartBeatValue = urlForHeartBeatValue,
                worksInQueueCount = worksInQueueCount,
                worksInErrorCount = worksInErrorCount,
                isKeepScreenOn = isKeepScreenOn,
                onCheckNotificationListeningClicked = onCheckNotificationListeningClicked,
                onKeepScreenOnCheckedChanged = onKeepScreenOnCheckedChanged,
                isOemOptimisationSettingsButtonVisible = isOemOptimisationSettingsButtonVisible,
                onOemOptimisationSettingsClicked = onOemOptimisationSettingsClicked,
                onNotificationListenerSettingsClicked = onNotificationListenerSettingsClicked,
                onRestartServiceClicked = onRestartServiceClicked,
                onRestartAppClicked = onRestartAppClicked
            )
        }

        Text(
            modifier = Modifier
                .padding(8.dp)
                .clip(MaterialTheme.shapes.extraLarge)
                .combinedClickable(
                    onClick = {},
                    onLongClick = onAppVersionLongClicked,
                )
                .padding(8.dp),
            text = "App version: $appVersion",
            style = MaterialTheme.typography.bodyMedium
        )
    }
}

@Composable
private fun NotificationListenerStatus(
    modifier: Modifier = Modifier,
    listeningStatus: MainScreenState.NotificationListeningStatus,
    onAlertClicked: () -> Unit
) {
    val color by animateColorAsState(
        targetValue = when (listeningStatus) {
            MainScreenState.NotificationListeningStatus.NOT_LISTENING -> Color.Red
            MainScreenState.NotificationListeningStatus.LISTENING -> Color.Green
            MainScreenState.NotificationListeningStatus.CHECKING -> Color.Yellow
        }
    )

    Column(
        modifier = modifier
    ) {
        if (listeningStatus == MainScreenState.NotificationListeningStatus.NOT_LISTENING) {
            AsyncImage(
                modifier = Modifier
                    .size(100.dp)
                    .align(Alignment.CenterHorizontally)
                    .clickable(onClick = onAlertClicked),
                model = R.raw.ic_alert,
                contentDescription = null,
            )
        }

        Text(
            text = when (listeningStatus) {
                MainScreenState.NotificationListeningStatus.NOT_LISTENING -> "Прослушивание уведомлений не активно"
                MainScreenState.NotificationListeningStatus.LISTENING -> "Прослушивание уведомлений активно"
                MainScreenState.NotificationListeningStatus.CHECKING -> "Проверка прослушивания уведомлений..."
            },
            color = color,
            style = MaterialTheme.typography.headlineMedium,
            textAlign = TextAlign.Center
        )
    }
}

interface SelfUpdateContent {

    @Composable
    fun Content(modifier: Modifier = Modifier)

    operator fun invoke(modifier: Modifier): @Composable () -> Unit {
        return { Content(modifier) }
    }

    companion object {
        val EMPTY: SelfUpdateContent = object : SelfUpdateContent {
            @Composable
            override fun Content(modifier: Modifier) {
                // no-op
            }
        }
    }
}

@Preview(uiMode = Configuration.UI_MODE_NIGHT_YES)
@Composable
private fun SettingsScreenNodePreview() = PayPassTheme {
    Surface {
        MainScreenNode(
            modifier = Modifier.fillMaxSize(),
            userId = "1234567890",
            urlForPushValue = null,
            urlForSmsValue = null,
            urlForHeartBeatValue = null,
            worksInQueueCount = 0,
            worksInErrorCount = 0,
            notificationListeningStatus = MainScreenState.NotificationListeningStatus.NOT_LISTENING,
            appVersion = "1.0.0",
            onCheckNotificationListeningClicked = {},
            isKeepScreenOn = true,
            onKeepScreenOnCheckedChanged = {},
            isOemOptimisationSettingsButtonVisible = true,
            onOemOptimisationSettingsClicked = {},
            onNotificationListenerSettingsClicked = {},
            onRestartServiceClicked = {},
            onRestartAppClicked = {},
            onAppVersionLongClicked = {},
            onAlertClicked = {},
        )
    }
}