package dev.paypass.ui.component.app_conditions

import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import dev.paypass.domain.condition.AppCondition
import dev.paypass.ui.component.app_conditions.state.AppConditionListItemState
import dev.paypass.ui.component.app_conditions.widget.AppConditionsNotificationListenersDialog
import dev.paypass.ui.component.app_conditions.widget.AppConditionsPermissionDialog
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf

@Composable
fun AppConditionsScreenUI(
    modifier: Modifier = Modifier,
    component: AppConditionsScreenComponent,
) {
    val state by component.state.collectAsState()

    if (state.isShowingPermissionDialog) {
        AppConditionsPermissionDialog(
            onDismissRequest = component::onPermissionDialogDismissRequest,
            onOpenSettingsClicked = component::onPermissionDialogOpenSettingsClicked
        )
    }

    if (state.isShowingEnableNotificationListenerDialog) {
        AppConditionsNotificationListenersDialog(
            onDismissRequest = component::onEnableNotificationListenerDialogDismissRequest,
            onOpenSettingsClicked = component::onEnableNotificationListenerDialogOpenSettingsClicked
        )
    }

    AppConditionsScreenNode(
        modifier = modifier,
        items = state.itemStates,
        onConditionItemClicked = component::onConditionItemClicked
    )
}

@Composable
private fun AppConditionsScreenNode(
    modifier: Modifier = Modifier,
    items: ImmutableList<AppConditionListItemState> = persistentListOf(),
    onConditionItemClicked: (Int) -> Unit
) {
    Column(
        modifier = modifier
    ) {
        Spacer(modifier = Modifier.fillMaxHeight(0.1f))

        AppConditionsHeader(
            modifier = Modifier.padding(horizontal = 16.dp)
        )

        Spacer(Modifier.fillMaxHeight(0.1f))

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            contentAlignment = Alignment.Center
        ) {
            LazyColumn(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                items(
                    count = items.size,
                    key = { index -> items[index].id }
                ) { index ->
                    ConditionListItem(
                        modifier = Modifier
                            .fillMaxWidth()
                            .animateItem(),
                        item = items[index],
                        onItemClicked = { onConditionItemClicked(it.id) }
                    )
                }
            }
        }
    }
}

@Composable
private fun ConditionListItem(
    modifier: Modifier = Modifier,
    item: AppConditionListItemState,
    onItemClicked: (AppConditionListItemState) -> Unit
) {
    val borderColor = when (item.isSatisfied) {
        true -> Color.Green
        false -> MaterialTheme.colorScheme.error
    }

    Row(
        modifier = modifier
            .clip(MaterialTheme.shapes.medium)
            .clickable(
                onClick = { onItemClicked(item) }
            )
            .border(
                width = 1.dp,
                color = borderColor,
                shape = MaterialTheme.shapes.medium
            )
            .padding(8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        ConditionStatusIndicator(
            modifier = Modifier.size(24.dp),
            isSatisfied = item.isSatisfied
        )

        Spacer(Modifier.width(8.dp))

        Column {
            Text(
                text = when (item.appCondition) {
                    AppCondition.EnableNotificationListener -> "Получение push-уведомлений"
                    AppCondition.Permission.READ_SMS -> "Чтение SMS"
                    AppCondition.Permission.RECEIVE_SMS -> "Получение SMS"
                    AppCondition.Permission.POST_NOTIFICATIONS -> "Отправка уведомлений"
                    AppCondition.Permission.LOCATION -> "Доступ к местоположению"
                    AppCondition.Permission.BACKGROUND_LOCATION -> "Доступ к местоположению в фоне"
                    AppCondition.GpsEnabled -> "GPS включен"
                    AppCondition.IgnoreBatteryOptimization -> "Оптимизация батареи отключена"
                },
                style = MaterialTheme.typography.bodyMedium
            )
            Text(
                text = when (item.appCondition) {
                    AppCondition.EnableNotificationListener -> "Для получения push-уведомлений необходимо разрешить доступ к уведомлениям"
                    AppCondition.Permission.READ_SMS -> "Необходимо разрешить доступ к чтению SMS"
                    AppCondition.Permission.RECEIVE_SMS -> "Необходимо разрешить доступ к получению SMS"
                    AppCondition.Permission.POST_NOTIFICATIONS -> "Необходимо разрешить доступ к отправке уведомлений"
                    AppCondition.Permission.LOCATION -> "Необходимо разрешить доступ к местоположению"
                    AppCondition.Permission.BACKGROUND_LOCATION -> "Необходимо разрешить доступ к местоположению в фоне"
                    AppCondition.GpsEnabled -> "Необходимо включить GPS"
                    AppCondition.IgnoreBatteryOptimization -> "Необходимо отключить оптимизацию батареи для этого приложения"
                },
                style = MaterialTheme.typography.bodySmall
            )
        }
    }
}

@Composable
private fun ConditionStatusIndicator(
    modifier: Modifier = Modifier,
    isSatisfied: Boolean
) {
    Icon(
        modifier = modifier,
        imageVector = when (isSatisfied) {
            true -> Icons.Default.Check
            false -> Icons.Default.Close
        },
        contentDescription = null
    )
}

@Composable
private fun AppConditionsHeader(
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
    ) {
        Text(
            modifier = Modifier.align(Alignment.CenterHorizontally),
            text = "Разрешения",
            style = MaterialTheme.typography.headlineMedium
        )

        Spacer(Modifier.height(8.dp))

        Text(
            text = "Для корректной работы приложения требуется разрешить доступ к следующим функциям",
            style = MaterialTheme.typography.bodyMedium,
            textAlign = TextAlign.Center
        )
    }
}

@Preview
@Composable
private fun PermissionsScreenNodePreview() {
    Surface {
        AppConditionsScreenNode(
            modifier = Modifier.fillMaxSize(),
            items = persistentListOf(
                AppConditionListItemState(
                    id = 1,
                    appCondition = AppCondition.EnableNotificationListener,
                    isSatisfied = false
                ),
                AppConditionListItemState(
                    id = 2,
                    appCondition = AppCondition.Permission.READ_SMS,
                    isSatisfied = false
                ),
                AppConditionListItemState(
                    id = 3,
                    appCondition = AppCondition.Permission.RECEIVE_SMS,
                    isSatisfied = false
                ),
                AppConditionListItemState(
                    id = 4,
                    appCondition = AppCondition.Permission.POST_NOTIFICATIONS,
                    isSatisfied = false
                ),
                AppConditionListItemState(
                    id = 5,
                    appCondition = AppCondition.Permission.LOCATION,
                    isSatisfied = false
                ),
            ),
            onConditionItemClicked = {}
        )
    }
}