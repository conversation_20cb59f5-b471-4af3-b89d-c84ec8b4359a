package dev.paypass.ui.component.debug

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.BasicAlertDialog
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import dev.paypass.ui.core.text.AppAlertDialogContent
import dev.paypass.ui.theme.PayPassTheme

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppDebugDialog(
    modifier: Modifier = Modifier,
    component: AppDebugComponent,
    onDismissRequest: () -> Unit,
) {
    val state by component.state.collectAsStateWithLifecycle()

    BasicAlertDialog(
        modifier = modifier,
        onDismissRequest = onDismissRequest,
    ) {
        AppDebugDialogContent(
            isRemoteLogEnabled = state.isRemoteLogEnabled,
            onRemoteLogEnabledChanged = component::onRemoteLogEnabledChanged,
            isCancelSentNotificationEnabled = state.isCancelSentNotificationsEnabled,
            onCancelSentNotificationEnabledChanged = component::onCancelSentNotificationsEnableChanged
        )
    }
}

@Composable
private fun AppDebugDialogContent(
    modifier: Modifier = Modifier,
    isRemoteLogEnabled: Boolean,
    onRemoteLogEnabledChanged: (Boolean) -> Unit,
    isCancelSentNotificationEnabled: Boolean,
    onCancelSentNotificationEnabledChanged: (Boolean) -> Unit
) {
    AppAlertDialogContent(
        modifier = modifier
    ) {
        Column {
            Text(
                text = "Настройки",
                style = MaterialTheme.typography.headlineSmall
            )

            Column(
                modifier = Modifier.padding(top = 16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                SwitchItem(
                    title = "Удаленное логирование",
                    isChecked = isRemoteLogEnabled,
                    onCheckedChange = onRemoteLogEnabledChanged
                )

                SwitchItem(
                    title = "Отмена отправленных уведомлений",
                    isChecked = isCancelSentNotificationEnabled,
                    onCheckedChange = onCancelSentNotificationEnabledChanged
                )
            }
        }
    }
}

@Composable
private fun SwitchItem(
    modifier: Modifier = Modifier,
    title: String,
    isChecked: Boolean,
    onCheckedChange: (Boolean) -> Unit
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            modifier = Modifier.weight(1f),
            text = title,
            textAlign = TextAlign.Start,
        )

        Spacer(modifier = Modifier.width(8.dp))

        Switch(
            checked = isChecked,
            onCheckedChange = onCheckedChange
        )
    }
}

@Preview
@Composable
private fun AppDebugDialogContentPreview() = PayPassTheme {
    var isRemoteLogEnabled by remember { mutableStateOf(false) }

    AppDebugDialogContent(
        isRemoteLogEnabled = isRemoteLogEnabled,
        onRemoteLogEnabledChanged = { isRemoteLogEnabled = it },
        isCancelSentNotificationEnabled = false,
        onCancelSentNotificationEnabledChanged = {}
    )
}