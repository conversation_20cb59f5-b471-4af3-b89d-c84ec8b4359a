package dev.paypass.ui.component.auth

import android.content.res.Configuration
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import dev.paypass.ui.component.auth.state.AuthScreenState
import dev.paypass.ui.component.auth.widget.AuthBlock
import dev.paypass.ui.component.auth.widget.AuthQrCameraPermissionDialog
import dev.paypass.ui.theme.PayPassTheme

@Composable
internal fun AuthScreenUI(
    modifier: Modifier = Modifier,
    component: AuthScreenComponent,
) {
    val state by component.state.collectAsStateWithLifecycle()

    val focusManager = LocalFocusManager.current

    val cameraPermissionDialog = state.cameraPermissionDialog
    if (cameraPermissionDialog != null) {
        AuthQrCameraPermissionDialog(
            isDeniedForever = when (cameraPermissionDialog) {
                AuthScreenState.CameraPermissionDialog.DENIED -> false
                AuthScreenState.CameraPermissionDialog.DENIED_FOREVER -> true
            },
            onDismissRequest = component::onCameraPermissionDialogDismissRequest,
            onOpenSettingsClicked = component::onCameraPermissionDialogOpenSettingsClicked
        )
    }

    Surface(
        modifier = modifier
            .pointerInput(Unit) {
                detectTapGestures(
                    onTap = { focusManager.clearFocus() }
                )
            }
    ) {
        Column(
            modifier = Modifier
                .padding(16.dp)
        ) {
            Spacer(modifier = Modifier.weight(0.8F))

            Text(
                modifier = Modifier.align(Alignment.CenterHorizontally),
                text = "Авторизация нового устройства",
                style = MaterialTheme.typography.headlineMedium,
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.weight(0.1F))

            AuthBlock(
                modifier = Modifier
                    .imePadding()
                    .fillMaxWidth(),
                phoneFieldText = state.idFieldText,
                phoneFieldValueChanged = component::onIdFieldChanged,
                tokenFieldText = state.tokenFieldText,
                tokenFieldValueChanged = component::onTokenFieldChanged,
                onScanQrCodeClicked = {
                    focusManager.clearFocus()
                    component.onScanQrClicked()
                },
                isLoginButtonEnabled = state.isValid,
                onLoginButtonClicked = {
                    focusManager.clearFocus()
                    component.onLoginClicked()
                },
                isLoading = state.isLoading
            )

            Spacer(modifier = Modifier.weight(1F))
        }
    }
}

@Preview(uiMode = Configuration.UI_MODE_NIGHT_YES)
@Composable
private fun AuthScreenUiPreview() = PayPassTheme {
    AuthScreenUI(
        modifier = Modifier.fillMaxSize(),
        component = AuthScreenComponentStub()
    )
}