package dev.paypass.ui

import com.arkivanov.decompose.ComponentContext
import kotlin.reflect.KClass

interface ComponentFactory {

    fun <T : Any> createComponent(
        context: ComponentContext,
        componentClass: KClass<T>,
        params: Any? = null
    ): T
}

inline fun <reified T : Any> ComponentFactory.createComponent(
    context: ComponentContext,
    params: Any? = null
): T = createComponent(context, T::class, params)