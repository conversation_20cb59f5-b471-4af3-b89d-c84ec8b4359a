package dev.paypass.di

import com.arkivanov.decompose.ComponentContext
import dev.paypass.ui.ComponentFactory
import org.koin.core.parameter.parametersOf
import org.koin.mp.KoinPlatform
import kotlin.reflect.KClass

class KoinComponentFactory : ComponentFactory {

    override fun <T : Any> createComponent(
        context: ComponentContext,
        componentClass: KClass<T>,
        params: Any?
    ): T {
        return KoinPlatform.getKoin().get(componentClass) {
            parametersOf(context, params)
        }
    }
}