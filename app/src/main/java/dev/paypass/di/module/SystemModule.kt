package dev.paypass.di.module

import dev.paypass.system.activity.CurrentActivityProvider
import dev.paypass.system.activity.CurrentActivityProviderImpl
import dev.paypass.system.app.ApplicationManager
import dev.paypass.system.app.ApplicationManagerImpl
import dev.paypass.system.device.DeviceManager
import dev.paypass.system.device.DeviceManagerImpl
import dev.paypass.system.location.LocationManager
import dev.paypass.system.location.LocationManagerImpl
import dev.paypass.system.notification.NotificationListenerChecker
import dev.paypass.system.notification.NotificationListenerCheckerImpl
import dev.paypass.system.permission.PermissionsManager
import dev.paypass.system.permission.PermissionsManagerImpl
import dev.paypass.system.notification.SystemNotificationsManager
import dev.paypass.system.notification.SystemNotificationsManagerImpl
import dev.paypass.system.sms.SmsManager
import dev.paypass.system.sms.SmsManagerImpl
import dev.paypass.system.storage.StorageFactoryProvider
import dev.paypass.system.storage.impl.StorageFactoryProviderImpl
import dev.paypass.system.thirdparty.QrScanLauncher
import dev.paypass.system.thirdparty.QrScanLauncherImpl
import dev.paypass.system.toast.ToastManager
import dev.paypass.system.toast.ToastManagerImpl
import org.koin.core.module.dsl.new
import org.koin.core.module.dsl.singleOf
import org.koin.dsl.bind
import org.koin.dsl.module

val systemModule = module {
    single<DeviceManager> { new(::DeviceManagerImpl) }
    single<ApplicationManager> { new(::ApplicationManagerImpl) }
    single<CurrentActivityProvider>(createdAtStart = true) { new(::CurrentActivityProviderImpl) }
    single<SmsManager>(createdAtStart = true) { new(::SmsManagerImpl) }
    single<SystemNotificationsManager>(createdAtStart = true) { new(::SystemNotificationsManagerImpl) }
    single<NotificationListenerChecker> { new(::NotificationListenerCheckerImpl) }
    single<PermissionsManager> { new(::PermissionsManagerImpl) }
    single<LocationManager> { new(::LocationManagerImpl) }
    single<ToastManager> { new(::ToastManagerImpl) }

    singleOf(::StorageFactoryProviderImpl).bind<StorageFactoryProvider>()

    singleOf(::QrScanLauncherImpl).bind<QrScanLauncher>()
}