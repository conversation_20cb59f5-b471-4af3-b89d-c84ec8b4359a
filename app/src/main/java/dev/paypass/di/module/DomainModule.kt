package dev.paypass.di.module

import dev.paypass.domain.auth.use_case.AuthLoginUseCase
import dev.paypass.domain.auth.use_case.AuthLoginUseCaseImpl
import dev.paypass.domain.condition.GetAllAppConditionsUseCase
import dev.paypass.domain.condition.GetAllAppConditionsUseCaseImpl
import dev.paypass.domain.config.remote.use_case.RemoteConfigChangesUseCase
import dev.paypass.domain.config.remote.use_case.RemoteConfigChangesUseCaseImpl
import dev.paypass.domain.auth.use_case.AuthScanQrUseCase
import dev.paypass.domain.auth.use_case.AuthScanQrUseCaseImpl
import dev.paypass.domain.auth.use_case.AuthSaveConfigUseCase
import dev.paypass.domain.auth.use_case.AuthSaveConfigUseCaseImpl
import dev.paypass.domain.auth.use_case.IsAuthChangesUseCase
import dev.paypass.domain.auth.use_case.IsAuthChangesUseCaseImpl
import dev.paypass.domain.config.remote.use_case.StartPeriodicLoadConfigUseCase
import dev.paypass.domain.config.remote.use_case.StartPeriodicLoadConfigUseCaseImpl
import dev.paypass.domain.config.app.use_case.AppConfigChangesUseCase
import dev.paypass.domain.config.app.use_case.AppConfigChangesUseCaseImpl
import dev.paypass.domain.config.app.use_case.AppConfigUpdateUseCase
import dev.paypass.domain.config.app.use_case.AppConfigUpdateUseCaseImpl
import dev.paypass.domain.debug.RemoteLoggingByConfigUseCase
import dev.paypass.domain.debug.RemoteLoggingByConfigUseCaseImpl
import dev.paypass.domain.event.use_case.SaveSmsEventsUseCase
import dev.paypass.domain.event.use_case.SaveSmsEventsUseCaseImpl
import dev.paypass.domain.event.use_case.SaveSystemNotificationEventsUseCase
import dev.paypass.domain.event.use_case.SaveSystemNotificationEventsUseCaseImpl
import dev.paypass.domain.event.use_case.SendNotProcessedEventsUseCase
import dev.paypass.domain.event.use_case.SendNotProcessedEventsUseCaseImpl
import dev.paypass.domain.heartbeat.StartPeriodicPushHeartbeatUseCase
import dev.paypass.domain.heartbeat.StartPeriodicPushHeartbeatUseCaseImpl
import dev.paypass.domain.heartbeat.StartPeriodicSendHeartbeatUseCase
import dev.paypass.domain.heartbeat.StartPeriodicSendHeartbeatUseCaseImpl
import dev.paypass.domain.notification.StartNotificationManagerUseCase
import dev.paypass.domain.notification.StartNotificationManagerUseCaseImpl
import dev.paypass.domain.selfupdate.use_case.CheckForUpdatesUseCase
import dev.paypass.domain.selfupdate.use_case.CheckForUpdatesUseCaseImpl
import dev.paypass.domain.selfupdate.use_case.RequestInstallAppUseCase
import dev.paypass.domain.selfupdate.use_case.RequestInstallAppUseCaseImpl
import dev.paypass.domain.selfupdate.use_case.SelfUpdateStatusChangesUseCase
import dev.paypass.domain.selfupdate.use_case.SelfUpdateStatusChangesUseCaseImpl
import dev.paypass.domain.selfupdate.use_case.StartDownloadAppUseCase
import dev.paypass.domain.selfupdate.use_case.StartDownloadAppUseCaseImpl
import org.koin.core.module.dsl.new
import org.koin.dsl.module

val domainModule = module {
    factory<GetAllAppConditionsUseCase> { new(::GetAllAppConditionsUseCaseImpl) }

    factory<StartNotificationManagerUseCase> { new(::StartNotificationManagerUseCaseImpl) }

    factory<StartPeriodicLoadConfigUseCase> { new(::StartPeriodicLoadConfigUseCaseImpl) }
    factory<RemoteConfigChangesUseCase> { new(::RemoteConfigChangesUseCaseImpl) }

    factory<AuthScanQrUseCase> { new(::AuthScanQrUseCaseImpl) }
    factory<AuthSaveConfigUseCase> { new(::AuthSaveConfigUseCaseImpl) }

    factory<IsAuthChangesUseCase> { new(::IsAuthChangesUseCaseImpl) }
    factory<AuthLoginUseCase> { new(::AuthLoginUseCaseImpl) }

    factory<SaveSmsEventsUseCase> { new(::SaveSmsEventsUseCaseImpl) }
    factory<SaveSystemNotificationEventsUseCase> { new(::SaveSystemNotificationEventsUseCaseImpl) }
    factory<SendNotProcessedEventsUseCase> { new(::SendNotProcessedEventsUseCaseImpl) }
    factory<StartPeriodicSendHeartbeatUseCase> { new(::StartPeriodicSendHeartbeatUseCaseImpl) }
    factory<StartPeriodicPushHeartbeatUseCase> { new(::StartPeriodicPushHeartbeatUseCaseImpl) }

    // self-update
    factory<CheckForUpdatesUseCase> { new(::CheckForUpdatesUseCaseImpl) }
    factory<SelfUpdateStatusChangesUseCase> { new(::SelfUpdateStatusChangesUseCaseImpl) }
    factory<StartDownloadAppUseCase> { new(::StartDownloadAppUseCaseImpl) }
    factory<RequestInstallAppUseCase> { new(::RequestInstallAppUseCaseImpl) }

    // app config
    factory<AppConfigChangesUseCase> { new(::AppConfigChangesUseCaseImpl) }
    factory<AppConfigUpdateUseCase> { new(::AppConfigUpdateUseCaseImpl) }

    factory<RemoteLoggingByConfigUseCase> { RemoteLoggingByConfigUseCaseImpl(get(), get(), getAll()) }
}