package dev.paypass

import com.google.firebase.crashlytics.FirebaseCrashlytics
import dev.paypass.core.analytic.LogEvent
import dev.paypass.core.analytic.Logger

class FirebaseCrashlyticsLogger(
    private val crashlytics: FirebaseCrashlytics
) : Logger {

    override fun log(event: LogEvent) {
        when (event) {
            is LogEvent.Error -> {
                crashlytics.recordException(event.error)
            }

            is LogEvent.Action -> {
                crashlytics.log(event.message)
            }
        }
    }
}