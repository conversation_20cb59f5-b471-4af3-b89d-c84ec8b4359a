package dev.paypass

import androidx.core.bundle.Bundle
import com.google.firebase.analytics.FirebaseAnalytics
import dev.paypass.core.analytic.LogEvent
import dev.paypass.core.analytic.Logger

class FirebaseAnalyticsLogger(
    private val firebaseAnalytics: FirebaseAnalytics
) : Logger {

    override fun log(event: LogEvent) {
        when (event) {
            is LogEvent.Error -> {
                // Do nothing
            }

            is LogEvent.Action -> {
                firebaseAnalytics.logEvent(event.name, Bundle())
            }
        }
    }
}