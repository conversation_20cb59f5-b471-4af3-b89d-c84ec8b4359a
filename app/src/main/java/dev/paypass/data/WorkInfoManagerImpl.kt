package dev.paypass.data

import androidx.work.WorkInfo
import androidx.work.WorkManager
import androidx.work.WorkQuery
import dev.paypass.domain.work.WorkInfo as AppWorkInfo
import dev.paypass.domain.work.WorkInfoManager
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

class WorkInfoManagerImpl(
    private val workManager: WorkManager
) : WorkInfoManager {

    override fun allWorksChanges(): Flow<List<AppWorkInfo>> {
        val query = WorkQuery.fromTags("send_event")

        return workManager.getWorkInfosFlow(query)
            .map { workInfos ->
                workInfos.map { workInfo ->
                    AppWorkInfo(
                        id = workInfo.id.toString(),
                        status = when (workInfo.state) {
                            WorkInfo.State.ENQUEUED -> AppWorkInfo.Status.ENQUEUED
                            WorkInfo.State.RUNNING -> AppWorkInfo.Status.RUNNING
                            WorkInfo.State.SUCCEEDED -> AppWorkInfo.Status.SUCCEEDED
                            WorkInfo.State.FAILED -> AppWorkInfo.Status.FAILED
                            WorkInfo.State.BLOCKED -> AppWorkInfo.Status.BLOCKED
                            WorkInfo.State.CANCELLED -> AppWorkInfo.Status.CANCELLED
                        }
                    )
                }
            }
    }
}