package dev.paypass.data.selfupdate.work

import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.content.pm.ServiceInfo
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.work.CoroutineWorker
import androidx.work.Data
import androidx.work.ForegroundInfo
import androidx.work.WorkerParameters
import androidx.work.workDataOf
import dev.paypass.data.selfupdate.work.DownloadAppWorker.Input.Companion
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import org.koin.core.component.KoinComponent
import java.io.File
import java.net.HttpURLConnection
import java.net.URL

internal class DownloadAppWorker(
    private val context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params),
    KoinComponent {

    private var lastProgress = 0

    private val notificationManager =
        context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

    override suspend fun doWork(): Result {
        val input = Input.fromWorkerData(inputData)

        return withContext(Dispatchers.IO) {
            try {
                // Создаем канал уведомлений
                createNotificationChannel()

                // Запускаем работу в foreground с неопределенным прогрессом
                setForeground(createForegroundInfo(0, true))

                val outputFile = File(input.outputFilePath)
                val url = URL(input.apkUrl)

                // Получаем размер файла через HEAD запрос
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "HEAD"
                val fileSize = connection.contentLength.toLong()
                connection.disconnect()

                if (fileSize <= 0) {
                    return@withContext Result.failure()
                }

                // Скачиваем APK
                url.openStream().use { inputStream ->
                    outputFile.outputStream().use { outputStream ->
                        val buffer = ByteArray(4096)
                        var totalBytesRead = 0L
                        var bytesRead: Int

                        while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                            outputStream.write(buffer, 0, bytesRead)
                            totalBytesRead += bytesRead

                            // Обновляем прогресс через WorkManager
                            val progress = ((totalBytesRead.toFloat() / fileSize) * 100).toInt()

                            if (lastProgress != progress) {
                                lastProgress = progress

                                setProgress(workDataOf(DownloadAppSchedulerImpl.PROGRESS_KEY to progress))
                                setForeground(createForegroundInfo(progress, false))
                            }

                        }
                    }
                }

                val output = Output(
                    outputFilePath = outputFile.absolutePath
                )

                Result.success(output.toWorkerData())
            } catch (e: Exception) {
                e.printStackTrace()
                Result.retry()
            }
        }
    }

    private fun createNotificationChannel() {
        val channel = NotificationChannel(
            CHANNEL_ID,
            "App Update Download",
            NotificationManager.IMPORTANCE_LOW
        )
        notificationManager.createNotificationChannel(channel)
    }

    private fun createForegroundInfo(progress: Int, indeterminate: Boolean): ForegroundInfo {
        val notification = NotificationCompat.Builder(context, CHANNEL_ID)
            .setContentTitle("Загрузка обновления")
            .setSmallIcon(android.R.drawable.stat_sys_download)
            .setProgress(100, progress, indeterminate)
            .build()

        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            ForegroundInfo(
                NOTIFICATION_ID,
                notification,
                ServiceInfo.FOREGROUND_SERVICE_TYPE_DATA_SYNC
            )
        } else {
            ForegroundInfo(NOTIFICATION_ID, notification)
        }
    }

    @Serializable
    data class Input(
        val apkUrl: String,
        val outputFilePath: String
    ) {
        fun toWorkerData(): Data {
            return Data.Builder()
                .putString(APK_URL_KEY, apkUrl)
                .putString(OUTPUT_FILE_PATH, outputFilePath)
                .build()
        }

        companion object {
            private const val APK_URL_KEY = "apkUrl"
            private const val OUTPUT_FILE_PATH = DownloadAppWorker.OUTPUT_FILE_PATH

            fun fromWorkerData(data: Data): Input {
                return Input(
                    apkUrl = data.getString(APK_URL_KEY) ?: error("APK URL is missing"),
                    outputFilePath = data.getString(OUTPUT_FILE_PATH)
                        ?: error("Output file path is missing")
                )
            }
        }
    }

    data class Output(
        val outputFilePath: String
    ) {

        fun toWorkerData(): Data {
            return Data.Builder()
                .putString(OUTPUT_FILE_PATH, outputFilePath)
                .build()
        }

        companion object {
            private const val OUTPUT_FILE_PATH = DownloadAppWorker.OUTPUT_FILE_PATH
            private const val VERSION_KEY = DownloadAppWorker.VERSION_KEY

            fun fromWorkerData(data: Data): Output {
                return Output(
                    outputFilePath = data.getString(OUTPUT_FILE_PATH)
                        ?: error("Output file path is missing")
                )
            }
        }
    }

    companion object {
        private const val CHANNEL_ID = "app_update_download"
        private const val NOTIFICATION_ID = 1
        const val OUTPUT_FILE_PATH = "output_file_path"
        const val APK_URL_KEY = "apkUrl"
        const val VERSION_KEY = "version"
    }
}
