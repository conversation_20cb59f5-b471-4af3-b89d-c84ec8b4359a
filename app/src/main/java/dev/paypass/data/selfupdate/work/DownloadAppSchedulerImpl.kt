package dev.paypass.data.selfupdate.work

import androidx.work.Constraints
import androidx.work.ExistingWorkPolicy
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkInfo
import androidx.work.WorkManager
import dev.paypass.domain.selfupdate.DownloadAppScheduler
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

internal class DownloadAppSchedulerImpl(
    private val workManager: WorkManager,
) : DownloadAppScheduler {

    override fun schedule(apkUrl: String, version: Long, outputFilePath: String) {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .build()

        val inputData = DownloadAppWorker.Input(
            apkUrl = apkUrl,
            outputFilePath = outputFilePath
        )

        val downloadWorkRequest = OneTimeWorkRequestBuilder<DownloadAppWorker>()
            .setInputData(inputData.toWorkerData())
            .addTag(WORKER_TAG)
            .addTag(createVersionTag(version))
            .setConstraints(constraints)
            .build()

        workManager.beginUniqueWork(
            UNIQUE_WORK_NAME,
            ExistingWorkPolicy.REPLACE,
            downloadWorkRequest
        ).enqueue()
    }

    override fun getDownloadStatus(): Flow<DownloadAppStatus> {
        return workManager.getWorkInfosByTagFlow(WORKER_TAG)
            .map { workInfoList ->
                val statuses = workInfoList.mapNotNull { workInfo ->
                    when (workInfo.state) {
                        WorkInfo.State.RUNNING, WorkInfo.State.ENQUEUED -> {
                            DownloadAppStatus.InProgress(
                                version = workInfo.tags.findVersion() ?: return@mapNotNull null,
                                progress = workInfo.progress.getInt(PROGRESS_KEY, -1),
                            )
                        }

                        WorkInfo.State.SUCCEEDED -> {
                            val output =
                                DownloadAppWorker.Output.fromWorkerData(workInfo.outputData)
                            DownloadAppStatus.Completed(
                                version = workInfo.tags.findVersion() ?: return@mapNotNull null,
                                outputFilePath = output.outputFilePath,
                            )
                        }

                        else -> null
                    }
                }.sortedBy {
                    when (it) {
                        is DownloadAppStatus.InProgress -> it.version
                        is DownloadAppStatus.Completed -> it.version
                        else -> 0
                    }
                }

                statuses.firstOrNull { it is DownloadAppStatus.InProgress }
                    ?: statuses.firstOrNull { it is DownloadAppStatus.Completed } ?: DownloadAppStatus.None
            }
    }

    private fun createVersionTag(version: Long) = "app_download_version_$version"

    private fun Set<String>.findVersion(): Long? {
        return this.firstOrNull { it.startsWith("app_download_version_") }
            ?.removePrefix("app_download_version_")
            ?.toLongOrNull()
    }

    companion object {
        private const val WORKER_TAG = "app_download"
        private const val UNIQUE_WORK_NAME = "app_download_work"
        const val PROGRESS_KEY = "progress"
    }
}
