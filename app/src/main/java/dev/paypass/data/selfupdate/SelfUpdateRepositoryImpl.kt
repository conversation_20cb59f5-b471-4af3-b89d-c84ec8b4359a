package dev.paypass.data.selfupdate

import android.content.Context
import dev.paypass.BuildConfig
import dev.paypass.data.config.firebase.FirebaseRemoteConfigService
import dev.paypass.domain.selfupdate.SelfUpdateRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.io.File

internal class SelfUpdateRepositoryImpl(
    private val context: Context,
    private val firebaseRemoteConfigService: FirebaseRemoteConfigService
) : SelfUpdateRepository {

    private val _updateStatus = MutableStateFlow<SelfUpdateRepository.UpdateStatus>(SelfUpdateRepository.UpdateStatus.LastVersionInstalled)

    override fun getUpdateStatus(): StateFlow<SelfUpdateRepository.UpdateStatus> = _updateStatus.asStateFlow()

    override suspend fun checkForUpdates() {
        firebaseRemoteConfigService.fetchAndActivate()

        val lastVersion = firebaseRemoteConfigService.getLastVersionBuildNumber()
        val currentVersion = BuildConfig.VERSION_CODE

        if (lastVersion > currentVersion) {
            val apkUrl = firebaseRemoteConfigService.getLastVersionApkUrl()
            if (apkUrl != null) {
                _updateStatus.value = SelfUpdateRepository.UpdateStatus.NewVersionAvailable(
                    version = lastVersion,
                    apkUrl = apkUrl
                )
            }
        }
    }

    override fun preparePathFoSave(apkUrl: String, version: Long): String {
        return File(context.filesDir, "update_$version.apk").absolutePath
    }
}