package dev.paypass.data.network

import android.util.Base64
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.buildJsonObject
import okhttp3.Interceptor
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import okio.Buffer
import retrofit2.Invocation
import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec

class EncryptionInterceptor(
    secretKey: String,
    algorithm: String,
    private val cipherTransformation: String
) : Interceptor {

    private val secretKeySpec = SecretKeySpec(secretKey.toByteArray(), algorithm)

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()

        val invocation = request.tag(Invocation::class.java) ?: error("Invocation not found")

        val encryptToFieldAnnotation = invocation.method().getAnnotation(EncryptToField::class.java) ?: return chain.proceed(request)

        val rawBody = Buffer().use {
            request.body?.writeTo(it)
            it.readByteString().utf8()
        }

        val cipher = Cipher.getInstance(cipherTransformation)
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec)

        val encryptedBytes = cipher.doFinal(rawBody.toByteArray())
        val encryptedMessage = Base64.encodeToString(encryptedBytes, Base64.DEFAULT)

        val encryptedBody = buildJsonObject {
            put(encryptToFieldAnnotation.fieldName, JsonPrimitive(encryptedMessage))
        }.toString().toRequestBody("application/json".toMediaType())

        val newRequest = request.newBuilder()
            .addHeader("Content-Type", "application/json")
            .method(request.method, encryptedBody)
            .build()

        return chain.proceed(newRequest)
    }

}