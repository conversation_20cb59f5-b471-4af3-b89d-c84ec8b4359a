package dev.paypass.data.network

import com.jakewharton.retrofit2.converter.kotlinx.serialization.asConverterFactory
import dev.paypass.data.network.allowed_packages.ExpectedPackagesResponse
import dev.paypass.data.network.config.ConfigRequestBody
import dev.paypass.data.network.config.ConfigResponseBody
import dev.paypass.data.network.event.EventRequestBody
import kotlinx.serialization.StringFormat
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import retrofit2.Response
import retrofit2.Retrofit
import retrofit2.create
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query
import retrofit2.http.Url

interface BackendApi {

    @EncryptToField("data")
    @POST
    suspend fun sendEvent(
        @Body requestBody: EventRequestBody,
        @Url url: String
    ): Response<Unit>

    @POST("api/MobileAppSettings")
    suspend fun getConfig(
        @Body requestBody: ConfigRequestBody
    ): Response<ConfigResponseBody>

    @GET("service/app_except.json")
    suspend fun getExpectedPackages(): Response<ExpectedPackagesResponse>

    @GET
    suspend fun sendHeartBeat(
        @Url url: String,
        @Query ("phoneNumber") phoneNumber: String,
        @Query ("version") version: String,
        @Query ("deviceId") deviceId: String,
        @Query ("latitude") latitude: Double? = null,
        @Query ("longitude") longitude: Double? = null,
        @Query ("deviceHealth") deviceHealth: String? = null,
        @Query ("apps") notificationListenerApps: String? = null
    ): Response<Unit>

    companion object {

        fun create(
            okHttpClient: OkHttpClient,
            stringFormat: StringFormat,
            baseUrl: String,
        ): BackendApi {
            val contentType = "application/json".toMediaType()

            val retrofit = Retrofit.Builder()
                .client(okHttpClient)
                .baseUrl(baseUrl)
                .addConverterFactory(stringFormat.asConverterFactory(contentType))
                .build()

            return retrofit.create()
        }
    }
}