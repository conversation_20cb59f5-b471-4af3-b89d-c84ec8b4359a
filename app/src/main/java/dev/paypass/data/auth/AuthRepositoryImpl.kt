package dev.paypass.data.auth

import dev.paypass.domain.auth.AuthInfo
import dev.paypass.domain.auth.AuthRepository
import kotlinx.coroutines.flow.Flow

internal class AuthRepositoryImpl(
    private val authInfoStorage: AuthInfoStorage,
) : AuthRepository {

    override fun authInfoChanges(): Flow<AuthInfo?> {
        return authInfoStorage.changesFlow()
    }

    override suspend fun saveAuth(id: String, token: String) {
        authInfoStorage.update(AuthInfo(id, token))
    }

    override suspend fun clearAuth() {
        authInfoStorage.clear()
    }
}