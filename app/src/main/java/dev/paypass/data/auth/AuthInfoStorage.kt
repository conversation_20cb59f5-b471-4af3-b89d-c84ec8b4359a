package dev.paypass.data.auth

import dev.paypass.domain.auth.AuthInfo
import dev.paypass.system.storage.Storage
import dev.paypass.system.storage.StorageFactoryProvider
import dev.paypass.system.storage.StorageType

internal class AuthInfoStorage(
    private val storageFactoryProvider: StorageFactoryProvider
) : Storage<AuthInfo> by storageFactoryProvider.getStorageFactory(
    storageType = StorageType.PERSISTENT
).createStorage(
    key = "auth_info",
    clazz = AuthInfo::class.java
)