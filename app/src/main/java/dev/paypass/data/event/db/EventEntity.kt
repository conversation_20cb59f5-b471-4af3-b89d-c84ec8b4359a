package dev.paypass.data.event.db

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "events_2")
data class EventEntity(
    @PrimaryKey()
    val id: Long,
    val type: Type,
    val timestamp: Long,
    val author: String,
    val message: String,
    val packageName: String? = null,
    val isProcessed: Boolean = false,
    val errorReasonType: ErrorReasonType? = null,
    val errorReasonServerCode: Int? = null,
    val errorReasonMessage: String? = null
) {

    enum class Type {
        SMS,
        NOTIFICATION
    }

    enum class ErrorReasonType {
        NO_INTERNET,
        SERVER_ERROR,
        UNKNOWN
    }
}