package dev.paypass.data.event

import dev.paypass.data.AppDatabase
import dev.paypass.data.event.db.EventEntity
import dev.paypass.domain.Event
import dev.paypass.domain.event.EventRepository
import dev.paypass.data.network.BackendApi
import dev.paypass.data.network.event.EventRequestBody
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber

internal class EventRepositoryImpl(
    database: AppDatabase,
    private val backendApi: BackendApi
) : EventRepository {

    private val scope = CoroutineScope(Dispatchers.IO)

    private val eventDao = database.eventDao()

    override fun notProcessedEvents(): Flow<List<Event>> {
        return eventDao.getNotProcessedEvents()
            .map { list -> list.map(EventMapper::databaseToDomain) }
            .filter { it.isNotEmpty() }
    }

    override suspend fun saveEvent(event: Event) {
        withContext(Dispatchers.IO) {
            val databaseNotificationEvent = EventMapper.domainToDatabase(event)

            eventDao.insertEvent(databaseNotificationEvent)
        }
    }

    override fun saveEventAsync(event: Event) {
        scope.launch { saveEvent(event) }
    }

    override suspend fun sendEvent(
        userId: String,
        userToken: String,
        url: String,
        event: Event
    ): Result<Unit> {
        return withContext(Dispatchers.IO) {
            Timber.d("Sending event: $event")

            val request = EventRequestBody(
                phoneNumber = userId,
                userToken = userToken,
                packageName = when (event) {
                    is Event.Notification -> event.packageName
                    is Event.Sms -> "sms.messaging"
                },
                message = formatMessage(event)
            )

            try {
                val response = backendApi.sendEvent(request, url)

                if (response.isSuccessful) {
                    Timber.d("Event sent successfully: $event")

                    if (event is Event.Notification) {
                        eventDao.deleteEvent(event.id)
                    } else {
                        eventDao.updateEvent(event.id, isProcessed = true)
                    }

                    eventDao.deleteEvent(event.id)
                    Result.success(Unit)
                } else {
                    val error = response.errorBody()?.string()

                    Timber.e("Failed to send event ${response.code()}: $error")

                    eventDao.updateEvent(
                        id = event.id,
                        isProcessed = false,
                        errorReasonType = EventEntity.ErrorReasonType.SERVER_ERROR,
                    )

                    Result.failure(Exception("Failed to send event ${response.code()}: $error"))
                }
            } catch (e: Exception) {
                Timber.e(e, "Failed to send event")

                eventDao.updateEvent(
                    id = event.id,
                    isProcessed = false,
                    errorReasonType = EventEntity.ErrorReasonType.UNKNOWN,
                )

                Result.failure(e)
            }
        }
    }

    private fun formatMessage(event: Event): String {
        return when (event) {
            is Event.Sms -> {
                "${event.phoneNumber} ${event.message}"
            }
            is Event.Notification -> {
                var message = "${event.title} ${event.message}"

                message = message
                    .replace("\\", " ")
                    .replace("+", " ")
                    .replace("#", " ")

                if (event.packageName == "ru.rshb.dbo") {
                    // Регулярное выражение для поиска чисел в формате "числа.3числа" и замена на "числа3числа"
                    message = message.replace(Regex("(\\d+)\\.(\\d{3})(?=\\D)"), "$1$2")
                    message = message.replace(Regex("(\\d+),(\\d{3})(?=\\D)"), "$1$2")
                }

                message
            }
        }
    }
}