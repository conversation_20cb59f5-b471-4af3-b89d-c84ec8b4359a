package dev.paypass.data.event.work

import android.content.Context
import androidx.work.CoroutineWorker
import androidx.work.Data
import androidx.work.WorkerParameters
import dev.paypass.core.analytic.AppAction
import dev.paypass.core.analytic.Logger
import dev.paypass.core.analytic.error
import dev.paypass.data.work.getSerialized
import dev.paypass.data.work.putSerialized
import dev.paypass.domain.Event
import dev.paypass.domain.event.EventRepository
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

internal class SendEventWorker(
    context: Context,
    workerParams: WorkerParameters
) : CoroutineWorker(context, workerParams),
    KoinComponent {

    private val eventRepository: EventRepository by inject()
    private val logger: Logger by inject()

    override suspend fun doWork(): Result {
        val input = runCatching { Input.fromWorkerData(inputData) }
            .getOrElse { error ->
                logger.error(error, "Failed to parse input data. input data: $inputData")
                return Result.failure()
            }

        val result = eventRepository.sendEvent(
            userId = input.userId,
            userToken = input.userToken,
            url = input.url,
            event = input.event
        )

        return if (result.isSuccess) {
            logger.log(AppAction.OnEventSend.Success(input.event))
            Result.success()
        } else {
            logger.log(AppAction.OnEventSend.Failure(input.event, result.exceptionOrNull(), runAttemptCount))

            if (runAttemptCount < DEFAULT_MAX_ATTEMPTS) {
                Result.retry()
            } else {
                Result.failure()
            }
        }
    }

    data class Input(
        val userId: String,
        val userToken: String,
        val url: String,
        val event: Event
    ) {

        fun toWorkerData(): Data {
            return Data.Builder()
                .putString(INPUT_DATA_USER_ID_KEY, userId)
                .putString(INPUT_DATA_USER_TOKEN_KEY, userToken)
                .putString(INPUT_DATA_URL_KEY, url)
                .putSerialized(INPUT_DATA_EVENT_KEY, event, Event.serializer())
                .build()
        }

        companion object {
            private const val INPUT_DATA_USER_ID_KEY = "userId"
            private const val INPUT_DATA_USER_TOKEN_KEY = "userToken"
            private const val INPUT_DATA_URL_KEY = "url"
            private const val INPUT_DATA_EVENT_KEY = "event"

            fun fromWorkerData(data: Data): Input {
                return Input(
                    url = data.getString(INPUT_DATA_URL_KEY) ?: error("Host is missing"),
                    event = data.getSerialized(INPUT_DATA_EVENT_KEY, Event.serializer()) ?: error("Event is missing"),
                    userId = data.getString(INPUT_DATA_USER_ID_KEY) ?: error("User ID is missing"),
                    userToken = data.getString(INPUT_DATA_USER_TOKEN_KEY) ?: error("User token is missing")
                )
            }
        }
    }

    companion object {
        private const val DEFAULT_MAX_ATTEMPTS = 10
    }
}