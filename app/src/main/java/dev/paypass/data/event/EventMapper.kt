package dev.paypass.data.event

import dev.paypass.data.event.db.EventEntity
import dev.paypass.domain.Event

internal object EventMapper {

    fun domainToDatabase(event: Event): EventEntity {
        val type = when (event) {
            is Event.Sms -> EventEntity.Type.SMS
            is Event.Notification -> EventEntity.Type.NOTIFICATION
        }

        return EventEntity(
            id = event.id,
            type = type,
            timestamp = event.timestamp,
            author = when (event) {
                is Event.Sms -> event.phoneNumber
                is Event.Notification -> event.title
            },
            message = event.message,
            packageName = when (event) {
                is Event.Sms -> null
                is Event.Notification -> event.packageName
            },
            isProcessed = event.status is Event.Status.Processed,
            errorReasonType = when (event.status) {
                is Event.Status.Error.NoInternet -> EventEntity.ErrorReasonType.NO_INTERNET
                is Event.Status.Error.ServerError -> EventEntity.ErrorReasonType.SERVER_ERROR
                is Event.Status.Error.Unknown -> EventEntity.ErrorReasonType.UNKNOWN
                else -> null
            },
            errorReasonMessage = when (event.status) {
                is Event.Status.Error.ServerError -> (event.status as Event.Status.Error.ServerError).message
                is Event.Status.Error.Unknown -> (event.status as Event.Status.Error.Unknown).message
                else -> null
            }
        )
    }

    fun databaseToDomain(eventEntity: EventEntity): Event {
        val status = when {
            eventEntity.isProcessed -> Event.Status.Processed

            eventEntity.errorReasonType != null -> when (eventEntity.errorReasonType) {
                EventEntity.ErrorReasonType.NO_INTERNET -> Event.Status.Error.NoInternet

                EventEntity.ErrorReasonType.SERVER_ERROR -> Event.Status.Error.ServerError(
                    code = eventEntity.errorReasonServerCode ?: 0,
                    message = eventEntity.errorReasonMessage ?: ""
                )

                EventEntity.ErrorReasonType.UNKNOWN -> Event.Status.Error.Unknown(
                    message = eventEntity.errorReasonMessage ?: ""
                )
            }

            else -> Event.Status.New
        }

        return when (eventEntity.type) {
            EventEntity.Type.SMS -> Event.Sms(
                id = eventEntity.id,
                message = eventEntity.message,
                timestamp = eventEntity.timestamp,
                phoneNumber = eventEntity.author,
                status = status
            )

            EventEntity.Type.NOTIFICATION -> Event.Notification(
                id = eventEntity.id,
                message = eventEntity.message,
                timestamp = eventEntity.timestamp,
                title = eventEntity.author,
                packageName = eventEntity.packageName!!,
                status = status
            )
        }
    }
}