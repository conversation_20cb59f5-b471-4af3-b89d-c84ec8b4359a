package dev.paypass.data.event.db

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import kotlinx.coroutines.flow.Flow

@Dao
interface EventDao {

    @Query("SELECT * FROM events_2 WHERE isProcessed = 0 ORDER BY timestamp")
    fun getNotProcessedEvents(): Flow<List<EventEntity>>

    @Query("SELECT * FROM events_2 ORDER BY timestamp")
    suspend fun getEvents(): List<EventEntity>

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun insertEvent(event: EventEntity)

    @Query("UPDATE events_2 SET isProcessed = :isProcessed, errorReasonType = :errorReasonType, errorReasonServerCode = :errorReasonServerCode, errorReasonMessage = :errorReasonMessage WHERE id = :id")
    suspend fun updateEvent(
        id: Long,
        isProcessed: Boolean,
        errorReasonType: EventEntity.ErrorReasonType? = null,
        errorReasonServerCode: Int? = null,
        errorReasonMessage: String? = null
    )

    @Query("DELETE FROM events_2 WHERE id = :id")
    suspend fun deleteEvent(id: Long)
}