package dev.paypass.data.event.work

import androidx.work.BackoffPolicy
import androidx.work.Constraints
import androidx.work.ExistingWorkPolicy
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import dev.paypass.core.analytic.AppAction
import dev.paypass.core.analytic.Logger
import dev.paypass.domain.Event
import dev.paypass.domain.event.SendEventScheduler
import kotlin.time.Duration.Companion.minutes
import kotlin.time.toJavaDuration

internal class SendEventSchedulerImpl(
    private val workManager: WorkManager,
    private val logger: Logger
) : SendEventScheduler {

    override fun schedule(
        userId: String,
        userToken: String,
        url: String,
        event: Event,
    ) {
        val inputData = SendEventWorker.Input(
            userId = userId,
            userToken = userToken,
            url = url,
            event = event
        ).toWorkerData()

        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .build()

        val workRequest = OneTimeWorkRequestBuilder<SendEventWorker>()
            .setConstraints(constraints)
            .setBackoffCriteria(BackoffPolicy.LINEAR, DEFAULT_BACKOFF_DELAY.toJavaDuration())
            .setInputData(inputData)
            .addTag("send_event")
            .build()

        val workName = "send_event_${event.id}"

        val isWorkScheduled = workManager.getWorkInfosForUniqueWork(workName).get().any { it.state.isFinished.not() }

        workManager.enqueueUniqueWork(
            uniqueWorkName = workName,
            existingWorkPolicy = ExistingWorkPolicy.KEEP,
            request = workRequest
        )

        logger.log(AppAction.OnEventSend.Scheduled(event, isFirstAttempt = !isWorkScheduled))
    }

    companion object {
        private val DEFAULT_BACKOFF_DELAY = 1.minutes
    }
}