package dev.paypass.data.debug

import android.content.Context
import android.os.Build
import android.provider.Settings
import com.google.firebase.database.FirebaseDatabase
import dev.paypass.BuildConfig
import dev.paypass.core.analytic.AnalyticsDeviceDetails
import dev.paypass.domain.debug.RemoteLogger
import timber.log.Timber

internal class FirebaseDatabaseRemoteLogger(
    context: Context,
    firebaseDatabase: FirebaseDatabase,
) : RemoteLogger {

    private val deviceDetails = AnalyticsDeviceDetails(
        deviceId = Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID),
        osVersion = Build.VERSION.RELEASE,
        manufacturer = Build.MANUFACTURER,
        brand = Build.BRAND,
        device = Build.DEVICE,
        model = Build.MODEL,
        appVersionName = BuildConfig.VERSION_NAME,
        appVersionCode = BuildConfig.VERSION_CODE
    )

    private val firebaseDatabaseTimberTree = FirebaseDatabaseTimberTree(
        firebaseDatabase = firebaseDatabase,
        deviceDetails = deviceDetails,
    )

    override val timberTree: Timber.Tree
        get() = firebaseDatabaseTimberTree

    override fun setIdentifier(identifier: String) {
        firebaseDatabaseTimberTree.setKey(identifier)
    }
}