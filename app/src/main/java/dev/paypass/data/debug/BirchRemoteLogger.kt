package dev.paypass.data.debug

import android.content.Context
import android.provider.Settings
import com.gruffins.birch.Birch
import com.gruffins.birch.Level
import com.gruffins.birch.timber.BirchTree
import dev.paypass.domain.debug.RemoteLogger

internal class BirchRemoteLogger(
    private val context: Context
) : RemoteLogger {

    override val timberTree = BirchTree()

    init {
        Birch.init(
            context = context,
            apiKey = "c4e7b4bc90c3a04ad47b37b7c404a571",
            publicKey = "LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUF6czJqU0lWYVlHdFFQNlo1MHE3YgpZbWIrUXQxSTd1Y2I3R21FOW16U1NZblY1cWJPR2tkRFdhbnNkWndkWVVxSE1BVDZRSFhHL21idk1nVWRCVlhOCm80TXlFTUxzd01qS3gxVlBqVzltWGFOemVwcWhKTFhHSzV4MjkrWHd4S2xqYWxGcVU0cUR5TGgrNmEyM2RFbWYKd0t5YWN0V1BsSHBNb1BEek9ieWZRQ05WcDlzVVRuK1lMOWVBRU0yUGlGM0V6VWRCWmdiUXpPTVRlTUkwSzM1UgpQSEpRSGh1Z2wrWU5SZnBBMk9FV0hEN1RlcGpjeEovUHRGUGVyUm9NcTNtUVRYZ25tS2N4eWJmZDJibkJGeEIwCjF2dXlCYnNmSmtzZWpjUytwUERJL3BqL1M0aHZ6eUQvU3BQdit6dE02bVEvQURDQ3c0U2QrVG5jL0ZGRVlSTjIKQlFJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tCg=="
        )
        Birch.apply {
            level = Level.DEBUG
            console = false
            customProperties = mapOf("deviceId" to Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID))
        }
    }

    override fun setIdentifier(identifier: String) {
        Birch.identifier = identifier
    }
}