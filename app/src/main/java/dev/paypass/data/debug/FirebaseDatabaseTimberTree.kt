package dev.paypass.data.debug

import android.util.Log
import com.google.firebase.database.DatabaseReference
import com.google.firebase.database.FirebaseDatabase
import dev.paypass.core.analytic.AnalyticsDeviceDetails
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class FirebaseDatabaseTimberTree(
    private val firebaseDatabase: FirebaseDatabase,
    private val deviceDetails: AnalyticsDeviceDetails,
) : Timber.Tree() {

    private val scope = CoroutineScope(Dispatchers.IO)

    private val dateFormat = SimpleDateFormat("dd-MM-yyyy", Locale.getDefault())
    private val timeFormat = SimpleDateFormat("yyyy-MM-dd hh:mm:ss.SSS a zzz", Locale.getDefault())
    private val date = dateFormat.format(Date(System.currentTimeMillis()))

    private var databaseReference: DatabaseReference? = null

    fun setKey(key: String) {
        databaseReference = firebaseDatabase.getReference("logs/$date/${key}")
    }

    override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
        val timestamp = System.currentTimeMillis()
        val time = timeFormat.format(Date(timestamp))
        val remoteLog = RemoteLog(priorityAsString(priority), tag, message, t.toString(), time)

        scope.launch {
            databaseReference?.updateChildren(mapOf(Pair("-DeviceDetails", deviceDetails)))?.await()
            databaseReference?.child(timestamp.toString())?.setValue(remoteLog)?.await()
        }
    }

    private fun priorityAsString(priority: Int): String = when (priority) {
        Log.VERBOSE -> "VERBOSE"
        Log.DEBUG -> "DEBUG"
        Log.INFO -> "INFO"
        Log.WARN -> "WARN"
        Log.ERROR -> "ERROR"
        Log.ASSERT -> "ASSERT"
        else -> priority.toString()
    }

    data class RemoteLog(
        val priority: String,
        val tag: String?,
        val message: String,
        val throwable: String,
        val time: String
    )
}