package dev.paypass.data.config

import dev.paypass.data.auth.error.UserNotFoundException
import dev.paypass.data.config.mapper.ExpectedPackagesMapper
import dev.paypass.data.config.mapper.RemoteConfigMapper
import dev.paypass.data.config.storage.ExpectedPackagesStorage
import dev.paypass.data.config.storage.RemoteConfigStorage
import dev.paypass.domain.config.remote.RemoteConfig
import dev.paypass.domain.config.remote.RemoteConfigRepository
import dev.paypass.data.network.BackendApi
import dev.paypass.data.network.config.ConfigRequestBody
import dev.paypass.domain.config.remote.ExpectedPackages
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filterNotNull
import timber.log.Timber

internal class RemoteConfigRepositoryImpl(
    private val backendApi: BackendApi,
    private val remoteConfigStorage: RemoteConfigStorage,
    private val expectedPackagesStorage: ExpectedPackagesStorage
) : RemoteConfigRepository {

    override suspend fun getRemoteConfigOrNull(): RemoteConfig? {
        return remoteConfigStorage.getOrNull()
    }

    override suspend fun getExpectedPackagesOrNull(): ExpectedPackages? {
        return expectedPackagesStorage.getOrNull()
    }

    override fun remoteConfigChanges(): Flow<RemoteConfig> {
        return remoteConfigStorage.changesFlow()
            .filterNotNull()
    }

    override fun expectedPackagesChanges(): Flow<ExpectedPackages> {
        return expectedPackagesStorage.changesFlow()
            .filterNotNull()
    }

    override suspend fun loadRemoteConfig(
        phoneNumber: String,
        token: String,
        appVersion: String
    ): Result<RemoteConfig> {
        val requestBody = ConfigRequestBody(
            phoneNumber = phoneNumber,
            token = token,
            pppVersion = appVersion
        )

        try {
            val response = backendApi.getConfig(requestBody)

            if (response.isSuccessful) {
                val body = response.body() ?: error("Response body is null")
                val remoteConfig = RemoteConfigMapper.networkToDomain(body)

                remoteConfigStorage.update(remoteConfig)

                return Result.success(remoteConfig)
            } else if (response.code() == 400) {
                return Result.failure(UserNotFoundException())
            } else {
                return Result.failure(Exception("Failed to load remote config: ${response.errorBody()?.string()}"))
            }
        } catch (e: Exception) {
            Timber.e(e, "Internal error")
            return Result.failure(Exception("Internal error: ${e.message}"))
        }
    }

    override suspend fun loadExpectedPackages(): Result<ExpectedPackages> {
        try {
            val response = backendApi.getExpectedPackages()

            if (response.isSuccessful) {
                val body = response.body() ?: error("Response body is null")
                val allowedPackages = ExpectedPackagesMapper.networkToDomain(body)

                expectedPackagesStorage.update(allowedPackages)

                return Result.success(allowedPackages)
            } else {
                return Result.failure(Exception("Failed to load allowed packages: ${response.errorBody()?.string()}"))
            }
        } catch (e: Exception) {
            Timber.e(e, "Internal error")
            return Result.failure(Exception("Internal error: ${e.message}"))
        }
    }
}