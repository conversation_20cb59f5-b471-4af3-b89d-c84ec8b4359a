package dev.paypass.data.config.storage

import dev.paypass.domain.config.remote.ExpectedPackages
import dev.paypass.system.storage.Storage
import dev.paypass.system.storage.StorageFactoryProvider
import dev.paypass.system.storage.StorageType

internal class ExpectedPackagesStorage(
    private val storageFactoryProvider: StorageFactoryProvider
) : Storage<ExpectedPackages> by storageFactoryProvider.getStorageFactory(
    storageType = StorageType.PERSISTENT
).createStorage(
    key = "expected_packages",
    clazz = ExpectedPackages::class.java
)