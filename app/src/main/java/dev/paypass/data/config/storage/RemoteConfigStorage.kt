package dev.paypass.data.config.storage

import dev.paypass.domain.config.remote.RemoteConfig
import dev.paypass.system.storage.Storage
import dev.paypass.system.storage.StorageFactoryProvider
import dev.paypass.system.storage.StorageType

internal class RemoteConfigStorage(
    private val storageFactoryProvider: StorageFactoryProvider
) : Storage<RemoteConfig> by storageFactoryProvider.getStorageFactory(
    storageType = StorageType.PERSISTENT
).createStorage(
    key = "remote_config",
    clazz = RemoteConfig::class.java
)