package dev.paypass.data.config

import dev.paypass.data.config.storage.AppConfigStorage
import dev.paypass.domain.config.app.AppConfig
import dev.paypass.domain.config.app.AppConfigRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.stateIn

internal class AppConfigRepositoryImpl(
    private val appConfigStorage: AppConfigStorage
) : AppConfigRepository {

    private val scope = CoroutineScope(Dispatchers.Default + SupervisorJob())

    override val config = appConfigStorage.changesFlow()
        .filterNotNull()
        .stateIn(scope, SharingStarted.Companion.Eagerly, AppConfig.Companion.Default)

    override suspend fun updateConfig(config: AppConfig) {
        appConfigStorage.update(config)
    }
}