package dev.paypass.data.config.mapper

import dev.paypass.domain.config.remote.RemoteConfig
import dev.paypass.data.network.config.ConfigResponseBody

internal object RemoteConfigMapper {

    fun networkToDomain(
        body: ConfigResponseBody
    ): RemoteConfig {
        return RemoteConfig(
            urlForPush = body.urlForPush,
            urlForSms = body.urlForSms,
            urlForHeartbeat = "https://ipeye.store/api/MobileHeartbeat/newbeat"
        )
    }
}