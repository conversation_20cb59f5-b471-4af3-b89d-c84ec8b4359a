package dev.paypass.data.config.storage

import dev.paypass.domain.config.app.AppConfig
import dev.paypass.system.storage.Storage
import dev.paypass.system.storage.StorageFactoryProvider
import dev.paypass.system.storage.StorageType

internal class AppConfigStorage(
    private val storageFactoryProvider: StorageFactoryProvider
) : Storage<AppConfig> by storageFactoryProvider.getStorageFactory(
    storageType = StorageType.PERSISTENT
).createStorage(
    key = "app_config",
    clazz = AppConfig::class.java,
    defaultValue = AppConfig.Companion.Default
)