package dev.paypass.data.config.firebase

import com.google.firebase.FirebaseApp
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.google.firebase.remoteconfig.FirebaseRemoteConfigSettings
import dev.paypass.R
import kotlinx.coroutines.tasks.await
import timber.log.Timber

internal class FirebaseRemoteConfigService(
    private val remoteConfig: FirebaseRemoteConfig
) {

    init {

    }

    suspend fun fetchAndActivate(): Boolean {
        val configSettings = FirebaseRemoteConfigSettings.Builder()
            .setMinimumFetchIntervalInSeconds(0)
            .build()
        remoteConfig.setConfigSettingsAsync(configSettings).await()
        remoteConfig.setDefaultsAsync(R.xml.remote_config_defaults).await()
        remoteConfig.fetch().addOnFailureListener {
            Timber.e(it, "Failed to fetch remote config")
        }.addOnSuccessListener {
            Timber.d("Remote config fetched")
        }
        remoteConfig.fetchAndActivate().addOnFailureListener {
            Timber.e(it, "Failed to activate remote config")
        }.addOnSuccessListener {
            Timber.d("Remote config activated $it")
        }
        return remoteConfig.fetchAndActivate().await()
    }

    fun getLastVersionBuildNumber(): Long {
        return remoteConfig.getLong(KEY_LAST_VERSION_BUILD_NUMBER)
    }

    fun getLastVersionApkUrl(): String? {
        val url = remoteConfig.getString(KEY_LAST_VERSION_APK_URL)
        return url.ifEmpty { null }
    }

    companion object {
        private const val MIN_FETCH_INTERVAL = 3600L // 1 hour
        private const val KEY_LAST_VERSION_BUILD_NUMBER = "last_version_build_number"
        private const val KEY_LAST_VERSION_APK_URL = "last_version_apk_url"
    }
}
