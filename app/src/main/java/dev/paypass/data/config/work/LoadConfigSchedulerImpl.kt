package dev.paypass.data.config.work

import androidx.work.Constraints
import androidx.work.NetworkType
import androidx.work.PeriodicWorkRequest
import androidx.work.WorkManager
import dev.paypass.domain.auth.AuthInfo
import dev.paypass.domain.config.remote.LoadConfigScheduler
import java.util.concurrent.TimeUnit

class LoadConfigSchedulerImpl(
    private val workManager: WorkManager,
) : LoadConfigScheduler {

    override fun schedule(authInfo: AuthInfo) {
        val inputData = LoadConfigWorker.Input(
            authInfo = authInfo,
            appVersion = "alpha"
        ).toWorkerData()

        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .build()

        val loadConfigWorkRequest = PeriodicWorkRequest.Builder(
            LoadConfigWorker::class.java,
            INTERVAL,
            INTERVAL_UNIT
        )
            .setInputData(inputData)
            .setConstraints(constraints)
            .build()

        workManager.enqueue(loadConfigWorkRequest)
    }

    companion object {
        private const val INTERVAL = 1L
        private val INTERVAL_UNIT = TimeUnit.DAYS
    }
}