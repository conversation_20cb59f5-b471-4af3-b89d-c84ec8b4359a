package dev.paypass.data.config.work

import android.content.Context
import androidx.work.CoroutineWorker
import androidx.work.Data
import androidx.work.WorkerParameters
import dev.paypass.data.work.getSerialized
import dev.paypass.data.work.putSerialized
import dev.paypass.domain.auth.AuthInfo
import dev.paypass.domain.config.remote.RemoteConfigRepository
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

internal class LoadConfigWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params),
    KoinComponent {

    private val remoteConfigRepository: RemoteConfigRepository by inject()

    override suspend fun doWork(): Result {
        val input = Input.fromWorkerData(inputData)

        val loadRemoteConfigResult = remoteConfigRepository.loadRemoteConfig(
            phoneNumber = input.authInfo.id,
            token = input.authInfo.token,
            appVersion = input.appVersion
        )

        val loadExpectedPackagesResult = remoteConfigRepository.loadExpectedPackages()

        return if (loadRemoteConfigResult.isSuccess && loadExpectedPackagesResult.isSuccess) {
            Result.success()
        } else {
            Result.retry()
        }
    }

    data class Input(
        val authInfo: AuthInfo,
        val appVersion: String
    ) {

        fun toWorkerData(): Data {
            return Data.Builder()
                .putSerialized(AUTH_INFO_KEY, authInfo, AuthInfo.serializer())
                .putString(APP_VERSION_KEY, appVersion)
                .build()
        }

        companion object {
            private const val AUTH_INFO_KEY = "authInfo"
            private const val APP_VERSION_KEY = "appVersion"

            fun fromWorkerData(data: Data): Input {
                val authInfo = data.getSerialized(AUTH_INFO_KEY, AuthInfo.serializer())
                    ?: error("Auth info is missing")
                val appVersion = data.getString(APP_VERSION_KEY) ?: error("App version is missing")

                return Input(authInfo, appVersion)
            }
        }
    }
}