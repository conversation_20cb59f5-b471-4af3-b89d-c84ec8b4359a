package dev.paypass.data

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import dev.paypass.data.event.db.EventDao
import dev.paypass.data.event.db.EventEntity

@Database(
    entities = [EventEntity::class],
    version = AppDatabase.VERSION
)
abstract class AppDatabase: RoomDatabase() {

    abstract fun eventDao(): EventDao

    companion object {

        internal const val VERSION = 2

        fun create(
            context: Context,
            fileName: String,
            migrations: Array<Migration>? = null
        ): AppDatabase {
            val appContext = context.applicationContext
            val dbFile = appContext.getDatabasePath(fileName)

            val databaseBuilder = Room.databaseBuilder(
                context = appContext,
                name = dbFile.absolutePath,
                klass = AppDatabase::class.java
            )

            return databaseBuilder
                .addMigrations(*migrations.orEmpty())
                .fallbackToDestructiveMigrationOnDowngrade()
                .build()
        }
    }
}