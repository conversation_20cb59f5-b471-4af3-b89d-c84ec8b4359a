package dev.paypass.data.work

import androidx.work.Data
import kotlinx.serialization.DeserializationStrategy
import kotlinx.serialization.SerializationStrategy
import kotlinx.serialization.json.Json

private val stringFormat = Json

internal fun <T> Data.Builder.putSerialized(key: String, value: T, serializationStrategy: SerializationStrategy<T>): Data.Builder {
    val string = stringFormat.encodeToString(serializationStrategy, value)
    putString(key, string)
    return this
}

internal fun <T> Data.getSerialized(key: String, serializationStrategy: DeserializationStrategy<T>): T? {
    val string = getString(key) ?: return null
    return stringFormat.decodeFromString(serializationStrategy, string)
}