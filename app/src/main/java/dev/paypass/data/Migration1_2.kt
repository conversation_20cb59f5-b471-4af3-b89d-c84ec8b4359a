package dev.paypass.data

import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

object Migration1_2 : Migration(1, 2) {

    override fun migrate(database: SupportSQLiteDatabase) {
        database.execSQL(
            """
            CREATE TABLE IF NOT EXISTS `events_2` (
                `id` INTEGER NOT NULL PRIMARY KEY,
                `errorReasonServerCode` INTEGER,
                `author` TEXT NOT NULL,
                `errorReasonType` TEXT,
                `isProcessed` INTEGER NOT NULL,
                `errorReasonMessage` TEXT,
                `packageName` TEXT,
                `type` TEXT NOT NULL,
                `message` TEXT NOT NULL,
                `timestamp` INTEGER NOT NULL
            )
            """.trimIndent()
        )
    }
}