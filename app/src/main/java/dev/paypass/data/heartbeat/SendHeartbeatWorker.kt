package dev.paypass.data.heartbeat

import android.content.Context
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import dev.paypass.domain.heartbeat.HeartbeatRepository
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

internal class SendHeartbeatWorker(
    context: Context,
    workerParams: WorkerParameters
) : CoroutineWorker(context, workerParams),
    KoinComponent {

    private val heartbeatRepository: HeartbeatRepository by inject()

    override suspend fun doWork(): Result {
        val heartbeatResult = heartbeatRepository.sendHeartbeat()

        return when {
            heartbeatResult.isSuccess -> Result.success()
            runAttemptCount < DEFAULT_MAX_ATTEMPTS -> Result.retry()
            else -> Result.failure()
        }
    }

    companion object {
        private const val DEFAULT_MAX_ATTEMPTS = 10
    }
}