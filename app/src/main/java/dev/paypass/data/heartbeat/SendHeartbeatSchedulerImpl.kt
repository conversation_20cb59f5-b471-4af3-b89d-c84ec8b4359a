package dev.paypass.data.heartbeat

import androidx.work.Constraints
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.NetworkType
import androidx.work.PeriodicWorkRequest
import androidx.work.WorkManager
import dev.paypass.domain.heartbeat.SendHeartbeatScheduler
import java.util.concurrent.TimeUnit

class SendHeartbeatSchedulerImpl(
    private val workManager: WorkManager,
) : SendHeartbeatScheduler {

    override fun schedule() {
        // Отменяем все ранее запланированные задачи heartbeat
        workManager.cancelAllWorkByTag("SendHeartbeatWorker")

        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .build()

        val workRequest = PeriodicWorkRequest.Builder(
            SendHeartbeatWorker::class.java,
            INTERVAL,
            INTERVAL_UNIT
        )
            .setConstraints(constraints)
            .build()

        workManager.enqueueUniquePeriodicWork(
            "SendHeartbeat",
            ExistingPeriodicWorkPolicy.CANCEL_AND_REENQUEUE,
            workRequest
        )
    }

    companion object {
        private const val INTERVAL = 15L
        private val INTERVAL_UNIT = TimeUnit.MINUTES
    }
}