package dev.paypass.data.heartbeat

import android.content.Context
import androidx.work.CoroutineWorker
import androidx.work.Data
import androidx.work.WorkerParameters
import dev.paypass.system.notification.NotificationListenerChecker
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

internal class PushHeartbeatWorker(
    appContext: Context,
    params: WorkerParameters
) : CoroutineWorker(appContext, params),
    KoinComponent{

    private val notificationListenerChecker: NotificationListenerChecker by inject()

    override suspend fun doWork(): Result {
        val output = Output(
            isCheckSuccess = notificationListenerChecker.check()
        )
        return Result.success(output.toWorkerData())
    }

    data class Output(
        val isCheckSuccess: Boolean
    ) {

        fun toWorkerData(): Data {
            return Data.Builder()
                .putBoolean(IS_CHECK_SUCCESS, isCheckSuccess)
                .build()
        }

        companion object {
            private const val IS_CHECK_SUCCESS = "is_check_success"
        }
    }
}