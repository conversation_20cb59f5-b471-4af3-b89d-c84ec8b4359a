package dev.paypass.data.heartbeat

import androidx.work.Constraints
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.NetworkType
import androidx.work.PeriodicWorkRequest
import androidx.work.WorkManager
import dev.paypass.domain.heartbeat.PushHeartbeatScheduler
import java.util.concurrent.TimeUnit

internal class PushHeartbeatSchedulerImpl(
    private val workManager: WorkManager,
) : PushHeartbeatScheduler {

    override fun schedule() {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .build()

        val workRequest = PeriodicWorkRequest.Builder(
            PushHeartbeatWorker::class.java,
            INTERVAL,
            INTERVAL_UNIT
        )
            .setConstraints(constraints)
            .build()

        workManager.enqueueUniquePeriodicWork(
            "PushHeartbeat",
            ExistingPeriodicWorkPolicy.CANCEL_AND_REENQUEUE,
            workRequest
        )
    }

    companion object {
        private const val INTERVAL = 15L
        private val INTERVAL_UNIT = TimeUnit.MINUTES
    }
}