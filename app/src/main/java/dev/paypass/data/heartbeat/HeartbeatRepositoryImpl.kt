package dev.paypass.data.heartbeat

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import android.provider.Settings
import dev.paypass.BuildConfig
import dev.paypass.core.analytic.AppAction
import dev.paypass.core.analytic.Logger
import dev.paypass.core.analytic.error
import dev.paypass.data.network.BackendApi
import dev.paypass.domain.auth.AuthRepository
import dev.paypass.domain.config.remote.RemoteConfigRepository
import dev.paypass.domain.heartbeat.HeartbeatRepository
import dev.paypass.system.device.DeviceManager
import dev.paypass.system.location.LocationManager
import dev.paypass.system.notification.SystemNotificationsManager
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import timber.log.Timber

internal class HeartbeatRepositoryImpl(
    private val context: Context,
    private val logger: Logger,
    private val backendApi: BackendApi,
    private val locationManager: LocationManager,
    private val deviceManager: <PERSON>ceManager,
    private val authRepository: AuthRepository,
    private val configRepository: RemoteConfigRepository,
    private val systemNotificationsManager: SystemNotificationsManager
) : HeartbeatRepository {

    @SuppressLint("HardwareIds")
    override suspend fun sendHeartbeat(): Result<Unit> {
        Timber.d("Sending heartbeat")

        val result = kotlin.runCatching {
            val authInfo = authRepository.authInfoChanges().firstOrNull()
                ?: error("Auth info is missing")
            val config = configRepository.remoteConfigChanges().firstOrNull()
                ?: error("Config is missing")
            val locationResult = runCatching { locationManager.getCurrentLocation() }

            val version = BuildConfig.VERSION_CODE
            val deviceModel = Build.MANUFACTURER + " " + Build.MODEL
            val deviceId = Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)

            val deviceHealth = buildString {
                append("batteryLevel: ${deviceManager.getBatteryLevel()}, ")
                append("batteryState: ${deviceManager.getBatteryState()}, ")
                append("thermalLevel: ${deviceManager.getThermalLevel()}")
            }

            val notificationListenerApps = runCatching {
                Json.encodeToJsonElement(systemNotificationsManager.fetchAllNotificationListenerApps().toList())
            }

            val response = backendApi.sendHeartBeat(
                url = config.urlForHeartbeat,
                phoneNumber = authInfo.id,
                version = "version: $version device: $deviceModel",
                deviceId = deviceId,
                latitude = locationResult.getOrNull()?.latitude,
                longitude = locationResult.getOrNull()?.longitude,
                deviceHealth = deviceHealth,
                notificationListenerApps = notificationListenerApps.getOrNull()?.toString()
            )

            if (response.isSuccessful.not()) {
                error("Failed to send heartbeat. response: ${response.errorBody()}")
            }
        }

        result.onSuccess {
            logger.log(AppAction.OnHeartbeatSend.Success)
        }

        result.onFailure {
            logger.error(it, "Failed to send heartbeat")
            logger.log(AppAction.OnHeartbeatSend.Failure(it))
        }

        return result
    }
}